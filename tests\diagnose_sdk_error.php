<?php
declare(strict_types=1);

// 阿里云短信SDK错误诊断工具
echo "=== 阿里云短信SDK错误诊断 ===\n";

// 加载依赖
$autoloadPaths = [
    __DIR__ . '/../boot.php',
    __DIR__ . '/../vendor/autoload.php'
];

$loaded = false;
foreach ($autoloadPaths as $path) {
    if (file_exists($path)) {
        try {
            require_once $path;
            if (class_exists('App\Lib\Msg\AliyunSms')) {
                $loaded = true;
                break;
            }
        } catch (\Throwable $e) {
            continue;
        }
    }
}

if (!$loaded) {
    echo "✗ 无法加载依赖\n";
    exit(1);
}

use App\Lib\Msg\AliyunSms;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 1. 检查环境变量配置
echo "\n1. 检查环境变量配置...\n";
$requiredEnvs = [
    'ALIYUN_SMS_ACCESS_KEY_ID' => 'AccessKey ID',
    'ALIYUN_SMS_ACCESS_KEY_SECRET' => 'AccessKey Secret', 
    'ALIYUN_SMS_SIGN_NAME' => '短信签名'
];

$envStatus = [];
foreach ($requiredEnvs as $env => $desc) {
    $value = getenv($env);
    if ($value !== false && $value !== '') {
        echo "   ✓ $desc ($env): " . substr($value, 0, 8) . "...\n";
        $envStatus[$env] = $value;
    } else {
        echo "   ✗ $desc ($env): 未配置\n";
        $envStatus[$env] = null;
    }
}

// 2. 检查网络连接
echo "\n2. 检查网络连接...\n";
$endpoints = [
    'dysmsapi.aliyuncs.com',
    'ecs.aliyuncs.com'  // 备用测试端点
];

foreach ($endpoints as $endpoint) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $result = @file_get_contents("https://$endpoint", false, $context);
    if ($result !== false || strpos($http_response_header[0] ?? '', '200') !== false) {
        echo "   ✓ $endpoint 可访问\n";
    } else {
        echo "   ✗ $endpoint 无法访问\n";
    }
}

// 3. 测试SDK初始化
echo "\n3. 测试SDK初始化...\n";
try {
    $logger = new Logger('diagnose');
    $logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));
    
    if ($envStatus['ALIYUN_SMS_ACCESS_KEY_ID'] && $envStatus['ALIYUN_SMS_ACCESS_KEY_SECRET']) {
        echo "   使用真实凭据测试...\n";
        $sms = new AliyunSms($logger);
        echo "   ✓ SDK初始化成功（真实凭据）\n";
    } else {
        echo "   使用测试凭据测试...\n";
        $sms = new AliyunSms($logger, 'test_ak', 'test_sk');
        echo "   ✓ SDK初始化成功（测试凭据）\n";
    }
} catch (\Throwable $e) {
    echo "   ✗ SDK初始化失败: " . $e->getMessage() . "\n";
    echo "   类型: " . get_class($e) . "\n";
    exit(1);
}

// 4. 测试参数验证
echo "\n4. 测试参数验证...\n";
$testCases = [
    ['phone' => '8613812345678', 'template' => 'SMS_123456', 'desc' => '有效参数'],
    ['phone' => '138123456', 'template' => 'SMS_123456', 'desc' => '无效手机号'],
    ['phone' => '8613812345678', 'template' => 'INVALID_123', 'desc' => '无效模板'],
];

foreach ($testCases as $case) {
    echo "   测试: " . $case['desc'] . "\n";
    try {
        $result = $sms->sendTemplateMessage($case['phone'], $case['template'], ['code' => '123456']);
        
        if ($result['success']) {
            echo "     ✓ 调用成功: " . $result['message'] . "\n";
        } else {
            echo "     ⚠ 调用失败: " . $result['message'] . "\n";
            if (isset($result['data']['error_type'])) {
                echo "     错误类型: " . $result['data']['error_type'] . "\n";
            }
        }
    } catch (\Throwable $e) {
        echo "     ✗ 异常: " . $e->getMessage() . "\n";
    }
}

// 5. 常见错误解决方案
echo "\n=== 常见SDK错误及解决方案 ===\n";

$commonErrors = [
    'InvalidAccessKeyId' => [
        'desc' => 'AccessKey ID无效',
        'solutions' => [
            '检查ALIYUN_SMS_ACCESS_KEY_ID是否正确',
            '确认AccessKey状态为"启用"',
            '检查AccessKey是否有短信服务权限'
        ]
    ],
    'SignatureDoesNotMatch' => [
        'desc' => '签名验证失败',
        'solutions' => [
            '检查ALIYUN_SMS_ACCESS_KEY_SECRET是否正确',
            '确认AccessKey和Secret匹配',
            '检查系统时间是否正确'
        ]
    ],
    'InvalidSignName' => [
        'desc' => '短信签名无效',
        'solutions' => [
            '检查ALIYUN_SMS_SIGN_NAME是否正确',
            '确认签名已审核通过',
            '签名名称要与控制台完全一致'
        ]
    ],
    'InvalidTemplateCode' => [
        'desc' => '模板编码无效',
        'solutions' => [
            '确认模板编码存在且已审核通过',
            '检查模板编码格式（如SMS_123456）',
            '确认模板变量与传入参数匹配'
        ]
    ],
    'Forbidden.RAM' => [
        'desc' => 'RAM权限不足',
        'solutions' => [
            '为RAM用户添加短信服务权限',
            '使用主账号AccessKey测试',
            '检查RAM策略配置'
        ]
    ]
];

foreach ($commonErrors as $code => $info) {
    echo "\n🔍 $code - " . $info['desc'] . "\n";
    foreach ($info['solutions'] as $i => $solution) {
        echo "   " . ($i + 1) . ". $solution\n";
    }
}

// 6. 下一步建议
echo "\n=== 下一步建议 ===\n";
if (!$envStatus['ALIYUN_SMS_ACCESS_KEY_ID'] || !$envStatus['ALIYUN_SMS_ACCESS_KEY_SECRET']) {
    echo "1. 配置正确的阿里云AccessKey\n";
    echo "2. 确保AccessKey有短信服务权限\n";
} else {
    echo "1. 检查阿里云控制台中的短信服务配置\n";
    echo "2. 确认签名和模板都已审核通过\n";
}

echo "3. 查看详细错误信息（在Web界面中查看完整响应）\n";
echo "4. 如需帮助，请提供完整的错误信息\n";

echo "\n=== 诊断完成 ===\n";
