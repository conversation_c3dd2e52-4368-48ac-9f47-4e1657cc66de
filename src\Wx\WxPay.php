<?php

namespace App\Lib\Wx;

use WeChatPay\Builder;
use WeChatPay\Util\PemUtil;
use WeChatPay\Formatter;
use WeChatPay\Crypto\AesGcm;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use App\Lib\Common;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class WxPay
{
    private $instance;
    private $logger;
    private $merchantPrivateKey;

    public function __construct()
    {
        $merchantId = $_ENV['WECHAT_MCH_ID'];
        $merchantSerialNumber = $_ENV['WECHAT_SERIAL_NO'];
        $privateKeyPath = $_ENV['WECHAT_PRIVATE_KEY_PATH'];
        $platformSerialNo = $_ENV['WECHAT_PLATFORM_SERIAL_NO'];
        $platformCertPath = $_ENV['WECHAT_PLATFORM_CERT_PATH'];

        // 添加检查
        if (!file_exists($privateKeyPath)) {
            throw new \Exception("证书文件不存在");
        }

        if (!file_exists($platformCertPath)) {
            throw new \Exception("微信平台证书文件不存在");
        }

        $merchantPrivateKey = PemUtil::loadPrivateKey($privateKeyPath);
        $this->merchantPrivateKey = $merchantPrivateKey;


        $platformCert = PemUtil::loadCertificate($platformCertPath);

        $this->instance = Builder::factory([
            'mchid' => $merchantId,
            'serial' => $merchantSerialNumber,
            'privateKey' => $merchantPrivateKey,
            'certs' => [
                $platformSerialNo => $platformCert
            ],
            'secret' => $_ENV['WECHAT_API_V3_KEY'],
            'merchant' => [],
        ]);

        $this->logger = new Logger('wechat_pay');  // 先初始化 Logger 实例
        $logDir = $_ENV['LOG_PATH'] . '/wechat/error/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        $this->logger->pushHandler(new \Monolog\Handler\RotatingFileHandler($logDir . 'error.log', 0, Logger::DEBUG));
    }

    private function getPlatformPublicKey(string $serialNumber): ?string
    {
        // 从证书存储中获取对应序列号的证书
        $certPath = $_ENV['WECHAT_PLATFORM_CERT_PATH'];

        if (!file_exists($certPath)) {
            $this->logger->error("平台证书不存在: $certPath");
            return null;
        }

        // 读取证书内容
        $certContent = file_get_contents($certPath);

        // 提取公钥
        $publicKeyResource = openssl_pkey_get_public($certContent);

        if (!$publicKeyResource) {
            $this->logger->error("从证书中提取公钥失败: " . openssl_error_string());
            return null;
        }

        // 获取公钥详细信息并提取公钥字符串
        $keyDetails = openssl_pkey_get_details($publicKeyResource);
        openssl_free_key($publicKeyResource); // 释放资源

        if (!isset($keyDetails['key'])) {
            $this->logger->error("无法获取公钥字符串");
            return null;
        }

        return $keyDetails['key'];
    }

    private function verifyRSASignature(
        string $signatureStr,
        string $signature,
        string $serialNumber
    ): bool {
        // 获取平台公钥
        $publicKey = $this->getPlatformPublicKey($serialNumber);

        if (!$publicKey) {
            $this->logger->error("未找到序列号对应的平台证书: $serialNumber");
            return false;
        }

        // 验证 RSA 签名
        $result = openssl_verify(
            $signatureStr,
            base64_decode($signature), // 微信签名是 Base64 编码的
            $publicKey,
            OPENSSL_ALGO_SHA256
        );

        return $result === 1;
    }

    private function detectDevice(): string
    {
        return Common::isMobile() ? 'mobile' : 'pc';
    }

    public function createOrder(array $orderInfo): array
    {
        $device = $this->detectDevice();
        $this->logger->info("检测到设备类型: $device");

        $params = [
            'appid' => $_ENV['WECHAT_APP_ID'],
            'mchid' => $_ENV['WECHAT_MCH_ID'],
            'description' => $orderInfo['description'],
            'out_trade_no' => $orderInfo['out_trade_no'],
            'notify_url' => $orderInfo['notify_url'],
            'amount' => ['total' => (int) $orderInfo['amount']],
        ];

        try {
            if ($device === 'pc') {
                $resp = $this->instance->chain('v3/pay/transactions/native')->post(['json' => $params]);
                $body = json_decode($resp->getBody()->getContents(), true);
                if (isset($body['code_url'])) {
                    $codeUrl = $body['code_url'];
                } else {
                    throw new \Exception("Native支付失败: " . json_encode($body));
                }

                $qrCode = QrCode::create($codeUrl)->setSize(300)->setMargin(10);
                $writer = new PngWriter();
                $result = $writer->write($qrCode);
                $qrBase64 = 'data:image/png;base64,' . base64_encode($result->getString());

                return ['type' => 'native', 'qrcode' => $qrBase64];
            } else {
                $params['scene_info'] = [
                    'payer_client_ip' => $_SERVER['REMOTE_ADDR'],
                    'h5_info' => ['type' => 'Wap'],
                ];
                $resp = $this->instance->chain('v3/pay/transactions/h5')->post(['json' => $params]);
                $body = json_decode($resp->getBody()->getContents(), true);
                if (isset($body['h5_url'])) {
                    $h5Url = $body['h5_url'];
                } else {
                    throw new \Exception("H5支付失败: " . json_encode($body));
                }

                // 为微信 H5 支付追加 redirect_url，便于用户完成支付后从微信返回商户结果页
                $redirectTarget = $_ENV['APP_URL'] . '/WxOrderquery.php?out_trade_no=' . rawurlencode($orderInfo['out_trade_no']);
                $redirectUrl = rawurlencode($redirectTarget);
                $separator = (strpos($h5Url, '?') !== false) ? '&' : '?';
                $finalUrl = $h5Url . $separator . 'redirect_url=' . $redirectUrl;

                //记录最终跳转地址，便于排查
                $this->logger->error('H5 跳转地址（含 redirect_url）: ' . $finalUrl);

                return ['type' => 'h5', 'url' => $finalUrl];
                //return ['type' => 'h5', 'url' => $h5Url];
            }
        } catch (\Exception $e) {
            $this->logger->error("支付创建失败: " . $e->getMessage());
            throw new \Exception("微信支付错误: " . $e->getMessage());
        }
    }

    /**
     * JSAPI 下单（微信内置浏览器）
     * 需要传入: description, out_trade_no, notify_url, amount(分), openid
     */
    public function createJsapiOrder(array $orderInfo): array
    {
        $params = [
            'appid' => $_ENV['WECHAT_APP_ID'],
            'mchid' => $_ENV['WECHAT_MCH_ID'],
            'description' => $orderInfo['description'],
            'out_trade_no' => $orderInfo['out_trade_no'],
            'notify_url' => $orderInfo['notify_url'],
            'amount' => ['total' => (int) $orderInfo['amount']],
            'payer' => ['openid' => $orderInfo['openid']],
        ];

        try {
            $resp = $this->instance->chain('v3/pay/transactions/jsapi')->post(['json' => $params]);
            $body = json_decode($resp->getBody()->getContents(), true);
            if (isset($body['prepay_id'])) {
                return ['type' => 'jsapi', 'prepay_id' => $body['prepay_id']];
            }
            throw new \Exception('JSAPI 下单失败: ' . json_encode($body));
        } catch (\Exception $e) {
            $this->logger->error('JSAPI 下单异常: ' . $e->getMessage());
            throw new \Exception('微信 JSAPI 下单错误: ' . $e->getMessage());
        }
    }

    /**
     * 生成 JSAPI 调起支付参数
     */
    public function generateJsapiPayParams(string $prepayId): array
    {
        $appId = $_ENV['WECHAT_APP_ID'];
        $timeStamp = (string) time();
        $nonceStr = bin2hex(random_bytes(16));
        $package = 'prepay_id=' . $prepayId;
        $message = $appId . "\n" . $timeStamp . "\n" . $nonceStr . "\n" . $package . "\n";

        openssl_sign($message, $rawSign, $this->merchantPrivateKey, OPENSSL_ALGO_SHA256);
        $paySign = base64_encode($rawSign);

        return [
            'appId' => $appId,
            'timeStamp' => $timeStamp,
            'nonceStr' => $nonceStr,
            'package' => $package,
            'signType' => 'RSA',
            'paySign' => $paySign,
        ];
    }

    public function handleNotify(callable $callback)
    {
        $inBody = file_get_contents('php://input');
        $headers = $this->getNormalizedHeaders();

        // 标准化请求头键名
        $headers = array_change_key_case($headers, CASE_UPPER);

        $inWechatpaySignature = $headers['WECHATPAY-SIGNATURE'] ?? '';
        $inWechatpaySignatureType = $headers['WECHATPAY-SIGNATURE-TYPE'] ?? '';
        $inWechatpayTimestamp = $headers['WECHATPAY-TIMESTAMP'] ?? '';
        $inWechatpaySerial = $headers['WECHATPAY-SERIAL'] ?? '';
        $inWechatpayNonce = $headers['WECHATPAY-NONCE'] ?? '';
        $apiv3Key = $_ENV['WECHAT_API_V3_KEY'];

        $this->logger->info("回调头信息: " . json_encode($headers));
        $this->logger->info("回调原始数据: " . $inBody);

        try {
            // 验证时间戳（5分钟内有效）
            $currentTimestamp = time();
            $wechatTimestamp = (int) $inWechatpayTimestamp;
            $timeDiff = $currentTimestamp - $wechatTimestamp;

            if (empty($inWechatpayTimestamp) || abs($timeDiff) > 300) {

                // 详细日志记录
                $this->logger->debug("时间戳验证详情", [
                    'server_time' => $currentTimestamp,
                    'wechat_time' => $wechatTimestamp,
                    'time_difference' => $timeDiff . '秒',
                    'timezone' => date_default_timezone_get()
                ]);

                $errorMessage = sprintf(
                    "时间戳验证失败：服务器时间(%s)与微信时间(%s)相差%d秒",
                    date('Y-m-d H:i:s', $currentTimestamp),
                    date('Y-m-d H:i:s', $wechatTimestamp),
                    abs($timeDiff)
                );

                throw new \Exception($errorMessage);
            }

            // 验证签名是否完整
            if (empty($inWechatpaySignature) || empty($inWechatpayNonce)) {
                throw new \Exception('签名信息不完整');
            }


            // 步骤2: 构造签名串并验证签名
            $signedStr = $inWechatpayTimestamp . "\n" . $inWechatpayNonce . "\n" . $inBody . "\n";

            // 根据签名类型选择验证方式
            $signatureValid = false;

            if (strtoupper($inWechatpaySignatureType) === 'WECHATPAY2-SHA256-RSA2048') {
                // RSA 签名验证
                $signatureValid = $this->verifyRSASignature(
                    $signedStr,
                    $inWechatpaySignature,
                    $inWechatpaySerial
                );
            } else {

                $expectedSignature = base64_encode(hash_hmac('sha256', $signedStr, $apiv3Key, true));

                $signatureValid = hash_equals($expectedSignature, $inWechatpaySignature);
            }

            if (!$signatureValid) {
                // 记录详细调试信息
                $this->logger->debug("签名验证失败详情: ", [
                    'provided_signature' => $inWechatpaySignature,
                    'calculated_signature' => $expectedSignature
                ]);
                throw new \Exception('Signature validation failed');
            }

            // 步骤3: 解密 resource（假设使用 AES-256-GCM）
            $bodyArray = json_decode($inBody, true);
            if (!isset($bodyArray['resource'])) {
                throw new \Exception('Invalid notify body');
            }

            $resource = $bodyArray['resource'];
            $ciphertext = base64_decode($resource['ciphertext']);
            $nonce = $resource['nonce'];
            $associatedData = $resource['associated_data'] ?? '';

            if (strlen($ciphertext) < 16) {
                throw new \Exception('Ciphertext too short for AES-GCM');
            }

            try {
                $tag = substr($ciphertext, -16);
                $encryptedData = substr($ciphertext, 0, -16);
                $decrypted = openssl_decrypt($encryptedData, 'aes-256-gcm', $apiv3Key, OPENSSL_RAW_DATA, $nonce, $tag, $associatedData);
                if ($decrypted === false) {

                    throw new \Exception('Decryption failed');
                }
            } catch (\Exception $e) {
                // 手动解密：分离认证标签（通常是密文末尾16字节）
                $this->logger->error($e->getMessage() . openssl_error_string());

            }

            $data = json_decode($decrypted, true);
            if ($data === null) {
                throw new \Exception('Invalid decrypted data');
            }

            $this->logger->info("支付回调数据: " . json_encode($data));

            // 只在成功事件时调用回调
            if (isset($data['trade_state']) && $data['trade_state'] === 'SUCCESS') {
                $callback($data);
            }

            header('Content-Type: application/json');
            echo json_encode(['code' => 'SUCCESS', 'message' => '成功']);
        } catch (\Exception $e) {
            $this->logger->error("回调处理失败: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['code' => 'FAIL', 'message' => $e->getMessage()]);
        }

    }

    public function queryOrder(string $outTradeNo): array
    {
        // 添加订单号调试日志
        $this->logger->info("开始查询订单，原始订单号: '{$outTradeNo}'");
        
        // 清理订单号，移除可能的特殊字符和空白
        $outTradeNo = trim($outTradeNo);
        
        // 确保订单号只包含合法字符
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $outTradeNo)) {
            throw new \Exception("Invalid order number format: {$outTradeNo}");
        }
        
        // 记录处理后的订单号
        $this->logger->info("处理后的订单号: '{$outTradeNo}'");
        
        try {
            // 使用魔术方法调用，避免 chain 方法的 normalize 处理
            // v3->pay->transactions->out_trade_no->{$outTradeNo}
            $endpoint = $this->instance->v3->pay->transactions;
            
            // 手动构建请求 URL，绕过 SDK 的 normalize
            $url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/' . $outTradeNo;
            $this->logger->info("完整请求 URL: '{$url}'");
            
            // 直接使用底层的 getDriver 方法获取 HTTP 客户端
            $client = $this->instance->getDriver();
            
            // 发送请求
            $resp = $client->request('GET', $url, [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'query' => ['mchid' => $_ENV['WECHAT_MCH_ID']]
            ]);
            
            $statusCode = $resp->getStatusCode();
            $this->logger->info("响应状态码: {$statusCode}");
            
            if ($statusCode !== 200) {
                throw new \Exception("Query failed with status: $statusCode");
            }
            
            $body = json_decode($resp->getBody()->getContents(), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON response: " . json_last_error_msg());
            }
            
            $this->logger->info("Order query successful for {$outTradeNo}: " . json_encode($body));
            return $body;
            
        } catch (\Exception $e) {
            $errorMsg = $e->getMessage();
            $this->logger->error("Order query failed for {$outTradeNo}: " . $errorMsg);
            
            // 如果是404错误，提供更友好的错误信息
            if (strpos($errorMsg, '404') !== false || strpos($errorMsg, 'ORDER_NOT_EXIST') !== false) {
                throw new \Exception("订单不存在: {$outTradeNo}");
            }
            
            throw new \Exception("微信支付查询错误: " . $errorMsg);
        }
    }

    private function getNormalizedHeaders(): array
    {
        // 兼容不同服务器环境的请求头获取
        if (function_exists('getallheaders')) {
            return getallheaders();
        }

        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) == 'HTTP_') {
                $header = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($key, 5)))));
                $headers[$header] = $value;
            }
        }
        return $headers;
    }
}