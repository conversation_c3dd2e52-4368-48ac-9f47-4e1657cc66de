<?php
declare(strict_types=1);

use App\Lib\Msg\AliyunSms;

// Web 场景优先引入 boot.php；CLI 使用 composer autoload + 可选 dotenv
if (PHP_SAPI !== 'cli' && is_file(__DIR__ . '/../boot.php')) {
    require_once __DIR__ . '/../boot.php';
} else {
    require_once __DIR__ . '/../vendor/autoload.php';
    if (file_exists(__DIR__ . '/../.env') && class_exists('Dotenv\\Dotenv')) {
        Dotenv\\Dotenv::createImmutable(dirname(__DIR__))->safeLoad();
    }
}

// 在代码中定义模板变量（你可自行按需修改）
$defaultParams = [
    // 'code' => '123456',
];

if (PHP_SAPI === 'cli') {
    // CLI：php examples/send_sms.php 86138xxxxxxx SMS_123456
    $phoneE164    = $argv[1] ?? '';
    $templateCode = $argv[2] ?? '';
    $params       = $defaultParams;

    if ($phoneE164 === '' || $templateCode === '') {
        fwrite(STDERR, "Usage: php examples/send_sms.php <phoneE164> <templateCode>\n");
        exit(2);
    }

    $sms = new AliyunSms();
    $ret = $sms->sendTemplateMessage($phoneE164, $templateCode, $params);
    echo json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
    exit(0);
}

// Web 界面：GET 显示表单，POST 发送短信
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $phoneE164    = trim($_POST['phoneE164'] ?? '');
    $templateCode = trim($_POST['templateCode'] ?? '');
    $params       = $defaultParams;

    $error = '';
    if ($phoneE164 === '' || $templateCode === '') {
        $error = '请输入手机号与模板编码';
    }

    $result = null;
    if ($error === '') {
        try {
            $sms = new AliyunSms();
            $result = $sms->sendTemplateMessage($phoneE164, $templateCode, $params);
        } catch (\Throwable $e) {
            $error = $e->getMessage();
        }
    }

    header('Content-Type: text/html; charset=utf-8');
    echo '<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">';
    echo '<title>发送短信 - 示例</title>';
    echo '<style>body{font-family:-apple-system,Segoe UI,Roboto,Arial,Helvetica,\"Hiragino Sans GB\",\"Microsoft YaHei\",sans-serif;padding:24px;}form{max-width:520px;margin:0 auto;border:1px solid #eee;border-radius:8px;padding:16px;}label{display:block;margin:12px 0 6px;}input[type=text]{width:100%;padding:10px;border:1px solid #ccc;border-radius:6px;}button{margin-top:16px;padding:10px 16px;border:0;border-radius:6px;background:#1677ff;color:#fff;cursor:pointer;}pre{background:#f7f7f7;padding:12px;border-radius:6px;overflow:auto;} .err{color:#c00;margin:8px 0;} .ok{color:#090;margin:8px 0;}</style>';
    echo '</head><body>';

    echo '<h3>发送短信（阿里云）</h3>';
    if ($error !== '') {
        echo '<div class="err">' . htmlspecialchars($error, ENT_QUOTES, 'UTF-8') . '</div>';
    }
    echo '<form method="post">';
    echo '<label>手机号（含国家码，不带+）</label>';
    echo '<input type="text" name="phoneE164" placeholder="86138xxxxxxx" value="' . htmlspecialchars($phoneE164 ?? '', ENT_QUOTES, 'UTF-8') . '">';
    echo '<label>模板编码</label>';
    echo '<input type="text" name="templateCode" placeholder="SMS_123456789" value="' . htmlspecialchars($templateCode ?? '', ENT_QUOTES, 'UTF-8') . '">';
    echo '<button type="submit">发送</button>';
    echo '</form>';

    if (is_array($result)) {
        $ok = $result['success'] ?? false;
        echo $ok ? '<div class="ok">发送成功</div>' : '<div class="err">发送失败</div>';
        echo '<pre>' . htmlspecialchars(json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8') . '</pre>';
    }

    echo '</body></html>';
    exit;
}

// 初次 GET：展示表单
header('Content-Type: text/html; charset=utf-8');
echo '<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">';
echo '<title>发送短信 - 示例</title>';
echo '<style>body{font-family:-apple-system,Segoe UI,Roboto,Arial,Helvetica,\"Hiragino Sans GB\",\"Microsoft YaHei\",sans-serif;padding:24px;}form{max-width:520px;margin:0 auto;border:1px solid #eee;border-radius:8px;padding:16px;}label{display:block;margin:12px 0 6px;}input[type=text]{width:100%;padding:10px;border:1px solid #ccc;border-radius:6px;}button{margin-top:16px;padding:10px 16px;border:0;border-radius:6px;background:#1677ff;color:#fff;cursor:pointer;}</style>';
echo '</head><body>';
echo '<h3>发送短信（阿里云）</h3>';
echo '<form method="post">';
echo '<label>手机号（含国家码，不带+）</label>';
echo '<input type="text" name="phoneE164" placeholder="86138xxxxxxx" value="">';
echo '<label>模板编码</label>';
echo '<input type="text" name="templateCode" placeholder="SMS_123456789" value="">';
echo '<button type="submit">发送</button>';
echo '</form>';
echo '</body></html>';
