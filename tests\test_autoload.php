<?php
declare(strict_types=1);

// 测试autoloader是否正常工作
echo "=== 测试Autoloader ===\n";

// 1. 测试boot.php加载
echo "1. 尝试加载 boot.php...\n";
$bootPath = __DIR__ . '/../boot.php';
if (file_exists($bootPath)) {
    echo "   ✓ boot.php 文件存在: $bootPath\n";
    try {
        require_once $bootPath;
        echo "   ✓ boot.php 加载成功\n";
    } catch (\Throwable $e) {
        echo "   ✗ boot.php 加载失败: " . $e->getMessage() . "\n";
        exit(1);
    }
} else {
    echo "   ✗ boot.php 文件不存在: $bootPath\n";
    exit(1);
}

// 2. 测试类是否可用
echo "\n2. 测试类加载...\n";
$testClasses = [
    'App\Lib\Msg\AliyunSms',
    'Monolog\Logger',
    'Monolog\Handler\StreamHandler'
];

foreach ($testClasses as $class) {
    if (class_exists($class)) {
        echo "   ✓ $class 类可用\n";
    } else {
        echo "   ✗ $class 类不可用\n";
    }
}

// 3. 测试AliyunSms实例化
echo "\n3. 测试 AliyunSms 实例化...\n";
try {
    $sms = new \App\Lib\Msg\AliyunSms(null, 'test_ak', 'test_sk');
    echo "   ✓ AliyunSms 实例化成功\n";
} catch (\Throwable $e) {
    echo "   ✗ AliyunSms 实例化失败: " . $e->getMessage() . "\n";
}

// 4. 测试环境变量
echo "\n4. 测试环境变量...\n";
$envVars = [
    'ALIYUN_SMS_ACCESS_KEY_ID',
    'ALIYUN_SMS_ACCESS_KEY_SECRET', 
    'ALIYUN_SMS_SIGN_NAME'
];

foreach ($envVars as $var) {
    $value = getenv($var);
    if ($value !== false && $value !== '') {
        echo "   ✓ $var = " . substr($value, 0, 10) . "...\n";
    } else {
        echo "   ✗ $var 未设置或为空\n";
    }
}

echo "\n=== 测试完成 ===\n";
