<?php
declare(strict_types=1);

namespace App\Lib\Msg;

use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi as DysmsClient;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageWithTemplateRequest;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageToGlobeRequest;

use Darabonba\OpenApi\Models\Config as OpenApiConfig;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

/**
 * 基础配置
 */
final class AliyunSmsBaseConfig
{
    /** @var string */
    public string $accessKeyId;
    /** @var string */
    public string $accessKeySecret;

    /** @var string 国内/国际默认统一 endpoint，可按需覆盖 */
    public string $endpointDomestic = 'dysmsapi.aliyuncs.com';
    public string $endpointInternational = 'dysmsapi.aliyuncs.com'; // 如需固定东南亚通道可改为 ap-southeast-1 专属域名

    /** @var string|null 国际短信默认 SenderId（From），部分国家/地区需要注册 */
    public ?string $defaultFrom = null;

    /** @var string|null 国内短信默认签名（SignName） */
    public ?string $defaultSignName = null;

    public function __construct(string $ak, string $sk)
    {
        $this->accessKeyId     = $ak;
        $this->accessKeySecret = $sk;
    }

    public static function fromEnv(): self
    {
        $ak = getenv('ALIYUN_SMS_ACCESS_KEY_ID') ?: getenv('ALIBABA_CLOUD_ACCESS_KEY_ID') ?: '';
        $sk = getenv('ALIYUN_SMS_ACCESS_KEY_SECRET') ?: getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET') ?: '';
        $cfg = new self($ak, $sk);
        if ($v = getenv('ALIYUN_SMS_SIGN_NAME')) {
            $cfg->defaultSignName = $v;
        }
        if ($v = getenv('ALIYUN_SMS_FROM')) {
            $cfg->defaultFrom = $v;
        }
        if ($v = getenv('ALIYUN_SMS_ENDPOINT')) {
            $cfg->endpointDomestic = $v;
        }
        if ($v = getenv('ALIYUN_SMS_INTL_ENDPOINT')) {
            $cfg->endpointInternational = $v;
        }
        return $cfg;
    }
}

/**
 * 统一返回结构：便于记录日志 & 业务判断
 */
final class SmsResult
{
    public bool $success = false;
    /** @var string|null 平台返回的业务码（如 OK、isv.BUSINESS_LIMIT_CONTROL 等） */
    public ?string $code = null;
    /** @var string|null 平台返回的信息 */
    public ?string $message = null;

    public ?string $requestId = null;  // 通用
    public ?string $bizId = null;      // 国内短信特有
    public ?string $messageId = null;  // 国际短信常见

    /** @var object|null 原始响应体（SDK -> body），用于深入排障 */
    public ?object $raw = null;

    public function toArray(): array
    {
        return [
            'success'   => $this->success,
            'code'      => $this->code,
            'message'   => $this->message,
            'requestId' => $this->requestId,
            'bizId'     => $this->bizId,
            'messageId' => $this->messageId,
            'raw'       => $this->raw,
        ];
    }

    /** 工具：从国内短信响应体构建 */
    public static function fromDomestic(object $body): self
    {
        $r = new self();
        $r->raw       = $body;
        $r->code      = property_exists($body, 'code') ? (string)$body->code : (property_exists($body, 'Code') ? (string)$body->Code : null);
        $r->message   = property_exists($body, 'message') ? (string)$body->message : (property_exists($body, 'Message') ? (string)$body->Message : null);
        $r->requestId = property_exists($body, 'requestId') ? (string)$body->requestId : (property_exists($body, 'RequestId') ? (string)$body->RequestId : null);
        $r->bizId     = property_exists($body, 'bizId') ? (string)$body->bizId : (property_exists($body, 'BizId') ? (string)$body->BizId : null);
        $r->success   = (strtoupper((string)($r->code ?? '')) === 'OK');
        return $r;
    }

    /** 工具：从国际短信响应体构建 */
    public static function fromInternational(object $body): self
    {
        $r = new self();
        $r->raw       = $body;
        // 各地域返回字段名可能略有差异，尽量兼容常见命名
        $r->code      = self::firstProp($body, ['code', 'Code', 'status', 'Status']);
        $r->message   = self::firstProp($body, ['message', 'Message', 'msg', 'Msg']);
        $r->requestId = self::firstProp($body, ['requestId', 'RequestId']);
        $r->messageId = self::firstProp($body, ['messageId', 'MessageId', 'id', 'Id']);
        // 只要 code 为空或 code/Status 不是错误常量，就按有 messageId 作为成功的强信号
        $r->success   = !empty($r->messageId) || strtoupper((string)($r->code ?? 'OK')) === 'OK';
        return $r;
    }

    private static function firstProp(object $obj, array $names): ?string
    {
        foreach ($names as $n) {
            if (property_exists($obj, $n) && $obj->{$n} !== null && $obj->{$n} !== '') {
                return (string)$obj->{$n};
            }
        }
        return null;
    }
}

/**
 * 国内短信（20170525）具体实现
 */
final class AliyunDomesticProvider
{
    private DysmsClient $client;

    public function __construct(AliyunSmsBaseConfig $cfg)
    {
        $openapi = new OpenApiConfig([
            'accessKeyId'     => $cfg->accessKeyId,
            'accessKeySecret' => $cfg->accessKeySecret,
        ]);
        $openapi->endpoint = $cfg->endpointDomestic;
        $this->client = new DysmsClient($openapi);
    }

    /**
     * 发送中国大陆短信（模板+签名）
     *
     * @param string|array $phoneNumbers 单个或多个手机号（必须带国家码，如 86138xxxxxxx）
     * @param string       $signName     短信签名（对应 SendMessageWithTemplate 的 from）
     * @param string       $templateCode 模板编号，如 SMS_123456
     * @param array        $templateParams 模板变量，自动转 JSON（UTF-8 / 不转义中文）
     */
    public function send($phoneNumbers, string $signName, string $templateCode, array $templateParams = []): SmsResult
    {
        $list = is_array($phoneNumbers) ? $phoneNumbers : (strpos((string)$phoneNumbers, ',') !== false ? explode(',', (string)$phoneNumbers) : [(string)$phoneNumbers]);
        $firstError = null;
        $lastOk = null;

        foreach ($list as $num) {
            $req = new SendMessageWithTemplateRequest([
                'to'            => trim($num),
                'from'          => $signName,
                'templateCode'  => $templateCode,
                'templateParam' => $templateParams ? json_encode($templateParams, JSON_UNESCAPED_UNICODE) : null,
            ]);

            $runtime = new RuntimeOptions([]);

            try {
                $resp = $this->client->sendMessageWithTemplateWithOptions($req, $runtime);
                $body = is_object($resp) && property_exists($resp, 'body') ? $resp->body : (object)[];
                $ok = strtoupper((string)($body->responseCode ?? '')) === 'OK';
                if ($ok) {
                    $r = new SmsResult();
                    $r->success     = true;
                    $r->code        = (string)($body->responseCode ?? 'OK');
                    $r->message     = (string)($body->responseDescription ?? 'OK');
                    $r->requestId   = isset($body->requestId) ? (string)$body->requestId : null;
                    $r->messageId   = isset($body->messageId) ? (string)$body->messageId : null;
                    $r->raw         = $body;
                    $lastOk = $r;
                } else {
                    $e = new SmsResult();
                    $e->success   = false;
                    $e->code      = isset($body->responseCode) ? (string)$body->responseCode : null;
                    $e->message   = isset($body->responseDescription) ? (string)$body->responseDescription : 'FAILED';
                    $e->requestId = isset($body->requestId) ? (string)$body->requestId : null;
                    $e->raw       = $body;
                    if ($firstError === null) {
                        $firstError = $e;
                    }
                }
            } catch (\Throwable $ex) {
                $msg  = ($ex instanceof TeaError) ? ($ex->message ?? 'TeaError') : $ex->getMessage();
                $code = ($ex instanceof TeaError) ? (int)($ex->code ?? 0) : (int)$ex->getCode();
                $e = new SmsResult();
                $e->success = false;
                $e->code    = (string)$code;
                $e->message = "Domestic SMS send failed: {$msg}";
                if ($firstError === null) {
                    $firstError = $e;
                }
            }
        }

        return $firstError !== null ? $firstError : ($lastOk ?? new SmsResult());
    }
}

/**
 * 国际/港澳台短信（20180501）具体实现
 */
final class AliyunInternationalProvider
{
    private DysmsClient $client;
    private ?string $defaultFrom;

    public function __construct(AliyunSmsBaseConfig $cfg)
    {
        $openapi = new OpenApiConfig([
            'accessKeyId'     => $cfg->accessKeyId,
            'accessKeySecret' => $cfg->accessKeySecret,
        ]);
        $openapi->endpoint = $cfg->endpointInternational;
        $this->client      = new DysmsClient($openapi);
        $this->defaultFrom = $cfg->defaultFrom;
    }

    /**
     * 发送国际/港澳台短信
     *
     * @param string      $to      目标号码（必须国家码+本地号，如 85261234567 / 12025550123）
     * @param string      $message 文本内容
     * @param string|null $from    SenderId（为空则使用默认）
     * @param array       $extras  透传可选字段：如 ['type'=>'OTP','tag'=>'login','channelId'=>'xxx']
     */
    public function send(string $to, string $message, ?string $from = null, array $extras = []): SmsResult
    {
        $req = new SendMessageToGlobeRequest([
            'to'      => $to,
            'message' => $message,
        ]);

        $useFrom = $from ?? $this->defaultFrom;
        if ($useFrom) {
            $req->from = $useFrom;
        }
        foreach (['type', 'tag', 'channelId'] as $opt) {
            if (array_key_exists($opt, $extras)) {
                $req->{$opt} = $extras[$opt];
            }
        }

        $runtime = new RuntimeOptions([]);

        try {
            $resp = $this->client->sendMessageToGlobeWithOptions($req, $runtime);
            $body = is_object($resp) && property_exists($resp, 'body') ? $resp->body : (object)[];
            return SmsResult::fromInternational($body);
        } catch (\Throwable $e) {
            $msg  = ($e instanceof TeaError) ? ($e->message ?? 'TeaError') : $e->getMessage();
            $code = ($e instanceof TeaError) ? (int)($e->code ?? 0) : (int)$e->getCode();
            $r = new SmsResult();
            $r->success = false;
            $r->code    = (string)$code;
            $r->message = "International SMS send failed: {$msg}";
            return $r;
        }
    }
}

/**
 * 策略适配器：统一对外服务
 */
final class AliyunSmsService
{
    private AliyunDomesticProvider $domestic;
    private AliyunInternationalProvider $international;
    private AliyunSmsBaseConfig $cfg;

    public function __construct(AliyunSmsBaseConfig $cfg)
    {
        $this->cfg            = $cfg;
        $this->domestic       = new AliyunDomesticProvider($cfg);
        $this->international  = new AliyunInternationalProvider($cfg);
    }

    /** 发送国内短信：签名 + 模板 */
    public function sendDomestic(
        $phoneNumbers,
        ?string $signName,
        string $templateCode,
        array $templateParams = []
    ): SmsResult {
        $sign = $signName ?: ($this->cfg->defaultSignName ?? '');
        return $this->domestic->send($phoneNumbers, $sign, $templateCode, $templateParams);
    }

    /** 发送国际/港澳台短信：纯文本（可带 SenderId/Type 等） */
    public function sendInternational(
        string $to,
        string $message,
        ?string $from = null,
        array $extras = []
    ): SmsResult {
        return $this->international->send($to, $message, $from, $extras);
    }
}
