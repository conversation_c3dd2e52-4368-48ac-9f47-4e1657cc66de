<?php
// examples/trigger_error.php
// 用途：人工触发异常/错误，验证 boot.php 的错误报警邮件

declare(strict_types=1);
require_once __DIR__ . '/../boot.php';

$type = $_GET['type'] ?? 'exception';

header('Content-Type: application/json; charset=utf-8');

// 帮助函数：在不同代码行抛出异常，形成不同签名（title|file|line）
function throw_runtime_exception_alt1(): void { throw new RuntimeException('测试未捕获异常：RuntimeException-2'); }
function trigger_user_error_alt1(): void { trigger_error('测试用户级致命错误：E_USER_ERROR-2', E_USER_ERROR); }

try {
    switch ($type) {
        case 'exception':
            throw new RuntimeException('测试未捕获异常：RuntimeException-1');
        case 'exception2':
            // 不同函数/行号，避免 5 分钟窗口内被去重
            throw_runtime_exception_alt1();
        case 'user_error':
            // 将被 set_error_handler 捕获，并在 E_USER_ERROR 时转为 ErrorException
            trigger_error('测试用户级致命错误：E_USER_ERROR-1', E_USER_ERROR);
            echo json_encode(['message' => '未触发']);
            break;
        case 'user_error2':
            trigger_user_error_alt1();
            echo json_encode(['message' => '未触发']);
            break;
        case 'fatal':
            // 通过调用未知函数名字符串触发致命错误
            $fn = '___function_not_exists_' . uniqid('a', true);
            $fn();
            echo json_encode(['message' => '未触发']);
            break;
        case 'fatal2':
            // 在不同代码行再次触发未知函数，形成不同签名
            $fn = '___function_not_exists_' . uniqid('b', true);
            $fn();
            echo json_encode(['message' => '未触发']);
            break;
        default:
            echo json_encode(['message' => '请提供 type=exception|exception2|user_error|user_error2|fatal|fatal2']);
    }
} catch (Throwable $e) {
    // 交给 boot.php 的 set_exception_handler 处理（该处理器会返回 500 与报警邮件）
    throw $e;
}
