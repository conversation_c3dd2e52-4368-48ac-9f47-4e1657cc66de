# 阿里云短信SDK错误解决指南

## 🚨 当前问题：SDK错误

您遇到的 `"error_type": "sdk_error"` 表明代码已正常运行，但阿里云SDK返回了错误。

## 🔍 立即诊断

运行诊断工具获取详细信息：
```bash
php tests/diagnose_sdk_error.php
```

## 📋 常见SDK错误及解决方案

### 1. 认证相关错误

#### `InvalidAccessKeyId`
**问题：** AccessKey ID无效或不存在
**解决方案：**
- 检查 `ALIYUN_SMS_ACCESS_KEY_ID` 环境变量
- 登录阿里云控制台确认AccessKey状态
- 确保AccessKey未被删除或禁用

#### `SignatureDoesNotMatch`
**问题：** 签名验证失败
**解决方案：**
- 检查 `ALIYUN_SMS_ACCESS_KEY_SECRET` 环境变量
- 确认AccessKey和Secret配对正确
- 检查系统时间是否准确（时间偏差过大会导致签名失败）

### 2. 权限相关错误

#### `Forbidden.RAM`
**问题：** RAM用户权限不足
**解决方案：**
- 为RAM用户添加 `AliyunDysmsFullAccess` 权限
- 或使用主账号AccessKey进行测试
- 检查RAM策略是否正确配置

#### `Forbidden.SubUser`
**问题：** 子账号权限不足
**解决方案：**
- 使用主账号AccessKey
- 为子账号授予短信服务权限

### 3. 短信服务相关错误

#### `InvalidSignName`
**问题：** 短信签名无效
**解决方案：**
- 检查 `ALIYUN_SMS_SIGN_NAME` 环境变量
- 确认签名已在控制台审核通过
- 签名名称必须与控制台完全一致（区分大小写）

#### `InvalidTemplateCode`
**问题：** 模板编码无效
**解决方案：**
- 确认模板编码存在且已审核通过
- 检查模板编码格式（如 `SMS_123456789`）
- 确认模板变量与传入参数匹配

#### `isv.TEMPLATE_MISSING_PARAMETERS`
**问题：** 模板参数缺失
**解决方案：**
- 检查模板中定义的变量
- 确保传入的参数包含所有必需变量
- 参数名称必须与模板中的变量名完全一致

### 4. 网络相关错误

#### `RequestTimeout`
**问题：** 请求超时
**解决方案：**
- 检查网络连接
- 确认防火墙设置
- 尝试更换网络环境

#### `ServiceUnavailable`
**问题：** 服务不可用
**解决方案：**
- 检查阿里云服务状态
- 稍后重试
- 联系阿里云技术支持

## 🛠️ 快速修复步骤

### 步骤1：验证环境变量
```bash
# 检查环境变量是否正确设置
echo $ALIYUN_SMS_ACCESS_KEY_ID
echo $ALIYUN_SMS_ACCESS_KEY_SECRET  
echo $ALIYUN_SMS_SIGN_NAME
```

### 步骤2：检查阿里云控制台
1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 进入 **短信服务** 控制台
3. 检查：
   - AccessKey状态（访问控制 > AccessKey管理）
   - 短信签名状态（短信服务 > 签名管理）
   - 短信模板状态（短信服务 > 模板管理）

### 步骤3：测试基本连接
```bash
# 测试网络连接
curl -I https://dysmsapi.aliyuncs.com

# 运行诊断工具
php tests/diagnose_sdk_error.php
```

### 步骤4：使用测试模板
如果您没有自己的模板，可以申请阿里云提供的测试模板：
- 模板编码：`SMS_154950909`
- 模板内容：`您的验证码为：${code}，该验证码5分钟内有效，请勿泄漏于他人！`

## 🔧 配置示例

### 环境变量配置
```bash
# Windows
set ALIYUN_SMS_ACCESS_KEY_ID=LTAI5t...
set ALIYUN_SMS_ACCESS_KEY_SECRET=abc123...
set ALIYUN_SMS_SIGN_NAME=测试签名

# Linux/Mac
export ALIYUN_SMS_ACCESS_KEY_ID=LTAI5t...
export ALIYUN_SMS_ACCESS_KEY_SECRET=abc123...
export ALIYUN_SMS_SIGN_NAME=测试签名
```

### .env文件配置
```env
ALIYUN_SMS_ACCESS_KEY_ID=LTAI5t...
ALIYUN_SMS_ACCESS_KEY_SECRET=abc123...
ALIYUN_SMS_SIGN_NAME=测试签名
ALIYUN_SMS_ENDPOINT=dysmsapi.aliyuncs.com
```

## 📞 获取帮助

### 查看详细错误信息
1. 使用Web界面发送测试短信
2. 查看返回的详细错误信息
3. 记录错误代码和错误消息

### 联系技术支持
如果问题仍未解决，请提供：
- 完整的错误信息
- 使用的AccessKey ID（前几位）
- 短信签名和模板编码
- 测试的手机号（脱敏）

### 有用的链接
- [阿里云短信服务文档](https://help.aliyun.com/product/44282.html)
- [短信服务错误码](https://help.aliyun.com/document_detail/101346.html)
- [RAM权限配置](https://help.aliyun.com/document_detail/93724.html)

---

**提示：** 大多数SDK错误都是配置问题，仔细检查控制台设置通常能解决问题。
