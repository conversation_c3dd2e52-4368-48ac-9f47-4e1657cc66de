# 阿里云短信测试工具使用说明

## 功能特性

✅ **Web UI界面** - 通过浏览器可视化操作  
✅ **CLI命令行** - 支持命令行验证测试  
✅ **实时发送** - 支持发送真实短信（需配置凭据）  
✅ **验证测试** - 手机号、模板编码格式验证  
✅ **错误处理** - 详细的错误信息和日志  

## 使用方法

### 🌐 Web界面使用

1. **启动Web服务器**
   ```bash
   # 使用PHP内置服务器
   php -S localhost:8080 -t tests/
   ```

2. **访问测试页面**
   ```
   http://localhost:8080/AliyunSmsTest.php
   ```

3. **配置环境变量**（发送真实短信需要）
   ```bash
   # Windows
   set ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
   set ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
   set ALIYUN_SMS_SIGN_NAME=your_sign_name
   
   # Linux/Mac
   export ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
   export ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
   export ALIYUN_SMS_SIGN_NAME=your_sign_name
   ```

### 💻 命令行使用

```bash
# 运行验证测试
php tests/AliyunSmsTest.php
```

## Web界面功能

### 📱 发送短信标签页

- **手机号码**：输入完整的E.164格式手机号（如：8613812345678）
- **模板编码**：阿里云控制台申请的模板编码（如：SMS_123456789）
- **模板参数**：JSON格式的模板变量（如：{"code": "123456"}）
- **短信签名**：可选，留空使用默认签名

### 🧪 验证测试标签页

运行内置的验证测试，检查：
- 手机号格式验证功能
- 模板编码格式验证功能
- 错误处理机制
- 返回数据结构

## 示例数据

### 有效的手机号格式
- `8613812345678` - 中国大陆
- `85261234567` - 香港
- `85366123456` - 澳门
- `88691234567` - 台湾
- `12025551234` - 美国

### 有效的模板编码格式
- `SMS_123456789`
- `SMS_987654321`

### 模板参数示例
```json
{
  "code": "123456",
  "product": "测试产品",
  "time": "5分钟"
}
```

## 配置说明

### 环境变量配置

| 变量名 | 说明 | 必需 |
|--------|------|------|
| `ALIYUN_SMS_ACCESS_KEY_ID` | 阿里云访问密钥ID | ✅ |
| `ALIYUN_SMS_ACCESS_KEY_SECRET` | 阿里云访问密钥Secret | ✅ |
| `ALIYUN_SMS_SIGN_NAME` | 短信签名 | ✅ |
| `ALIYUN_SMS_ENDPOINT` | 服务端点（可选） | ❌ |

### .env文件配置（可选）

在项目根目录创建 `.env` 文件：

```env
ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_SMS_SIGN_NAME=your_sign_name
ALIYUN_SMS_ENDPOINT=dysmsapi.aliyuncs.com
```

## 故障排除

### 常见问题

1. **"SMS服务未初始化"**
   - 检查环境变量是否正确配置
   - 确认阿里云凭据有效性

2. **"手机号格式非法"**
   - 确保手机号包含国家码
   - 检查手机号长度和格式

3. **"模板编码格式非法"**
   - 模板编码必须以 `SMS_` 开头
   - 后面必须是纯数字

4. **"缺少短信签名"**
   - 配置 `ALIYUN_SMS_SIGN_NAME` 环境变量
   - 或在发送时手动指定签名

### 调试技巧

1. **查看详细错误信息**
   - Web界面会显示完整的错误信息和响应数据
   - CLI模式会输出详细的日志信息

2. **验证配置**
   - 先运行验证测试确保基本功能正常
   - 再尝试发送真实短信

3. **检查网络连接**
   - 确保服务器可以访问阿里云API
   - 检查防火墙和代理设置

## 安全注意事项

⚠️ **重要提醒**

- 不要在生产环境中暴露此测试工具
- 妥善保管阿里云访问凭据
- 定期轮换访问密钥
- 监控短信发送量和费用

## 技术支持

如遇问题，请检查：
1. PHP版本兼容性（推荐PHP 7.4+）
2. 依赖包是否正确安装
3. 环境变量配置是否正确
4. 阿里云账户和权限设置

---

*最后更新：2025-08-13*
