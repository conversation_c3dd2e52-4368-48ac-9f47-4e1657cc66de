<?php
declare(strict_types=1);

use App\Lib\Msg\AliyunSms;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 简单的测试脚本，用于验证AliyunSms类的改进
class AliyunSmsTest
{
    private AliyunSms $sms;
    private Logger $logger;

    public function __construct()
    {
        // 创建测试日志器
        $this->logger = new Logger('test');
        $this->logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));
        
        // 创建SMS实例（使用测试密钥）
        $this->sms = new AliyunSms($this->logger, 'test_ak', 'test_sk');
    }

    public function testPhoneValidation(): void
    {
        echo "=== 测试手机号验证 ===\n";
        
        $testCases = [
            // 有效的手机号
            '8613812345678' => true,   // 中国大陆
            '85261234567' => true,     // 香港
            '85366123456' => true,     // 澳门
            '88691234567' => true,     // 台湾
            '12025551234' => true,     // 美国
            
            // 无效的手机号
            '138123456789' => false,   // 缺少国家码
            '86138123' => false,       // 太短
            '861381234567890123' => false, // 太长
            '86abc1234567' => false,   // 包含字母
            '' => false,               // 空字符串
        ];

        foreach ($testCases as $phone => $expected) {
            $result = $this->sms->sendTemplateMessage($phone, 'SMS_123456', ['code' => '123456']);
            $isValid = !str_contains($result['message'], '手机号格式非法');
            
            if ($isValid === $expected) {
                echo "✓ $phone: " . ($expected ? '有效' : '无效') . "\n";
            } else {
                echo "✗ $phone: 预期" . ($expected ? '有效' : '无效') . "，实际" . ($isValid ? '有效' : '无效') . "\n";
            }
        }
    }

    public function testTemplateCodeValidation(): void
    {
        echo "\n=== 测试模板编码验证 ===\n";
        
        $testCases = [
            'SMS_123456' => true,      // 有效格式
            'SMS_987654321' => true,   // 有效格式（长数字）
            'sms_123456' => false,     // 小写
            'SMS_abc123' => false,     // 包含字母
            'SMS123456' => false,      // 缺少下划线
            'TEMPLATE_123' => false,   // 错误前缀
            '' => false,               // 空字符串
        ];

        foreach ($testCases as $template => $expected) {
            $result = $this->sms->sendTemplateMessage('8613812345678', $template, ['code' => '123456']);
            $isValid = !str_contains($result['message'], '模板编码格式非法');
            
            if ($isValid === $expected) {
                echo "✓ $template: " . ($expected ? '有效' : '无效') . "\n";
            } else {
                echo "✗ $template: 预期" . ($expected ? '有效' : '无效') . "，实际" . ($isValid ? '有效' : '无效') . "\n";
            }
        }
    }

    public function testErrorHandling(): void
    {
        echo "\n=== 测试错误处理 ===\n";
        
        // 测试缺少签名
        $result = $this->sms->sendTemplateMessage('8613812345678', 'SMS_123456', ['code' => '123456']);
        if (str_contains($result['message'], '缺少短信签名')) {
            echo "✓ 正确检测到缺少签名错误\n";
        } else {
            echo "✗ 未能检测到缺少签名错误\n";
        }
        
        // 测试返回数据结构
        if (isset($result['success'], $result['message'], $result['data'])) {
            echo "✓ 返回数据结构正确\n";
        } else {
            echo "✗ 返回数据结构不正确\n";
        }
    }

    public function runAllTests(): void
    {
        echo "开始运行AliyunSms改进验证测试...\n\n";
        
        $this->testPhoneValidation();
        $this->testTemplateCodeValidation();
        $this->testErrorHandling();
        
        echo "\n测试完成！\n";
    }
}

// 如果直接运行此文件，执行测试
if (PHP_SAPI === 'cli' && basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    // 尝试加载autoloader
    $autoloadPaths = [
        __DIR__ . '/../vendor/autoload.php',
        __DIR__ . '/../boot.php'
    ];
    
    foreach ($autoloadPaths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
    
    try {
        $test = new AliyunSmsTest();
        $test->runAllTests();
    } catch (\Throwable $e) {
        echo "测试失败: " . $e->getMessage() . "\n";
        echo "请确保已正确安装依赖并配置autoloader\n";
    }
}
