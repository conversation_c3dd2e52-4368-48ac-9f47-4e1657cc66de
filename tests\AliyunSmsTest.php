<?php
declare(strict_types=1);

// 尝试多种方式加载依赖
$loaded = false;

// 方式1: 尝试加载boot.php
$bootPath = __DIR__ . '/../boot.php';
if (file_exists($bootPath)) {
    try {
        require_once $bootPath;
        $loaded = class_exists('App\Lib\Msg\AliyunSms');
    } catch (\Throwable $e) {
        // 继续尝试其他方式
    }
}

// 方式2: 如果boot.php失败，直接加载vendor/autoload.php
if (!$loaded) {
    $autoloadPath = __DIR__ . '/../vendor/autoload.php';
    if (file_exists($autoloadPath)) {
        require_once $autoloadPath;

        // 手动加载.env文件
        if (class_exists('Dotenv\Dotenv')) {
            $envPath = __DIR__ . '/..';
            if (file_exists($envPath . '/.env')) {
                $dotenv = \Dotenv\Dotenv::createImmutable($envPath);
                $dotenv->safeLoad();
            }
        }
        $loaded = class_exists('App\Lib\Msg\AliyunSms');
    }
}

// 方式3: 如果还是失败，手动包含必要的文件
if (!$loaded) {
    $srcPath = __DIR__ . '/../src/msg/AliyunSms.php';
    if (file_exists($srcPath)) {
        require_once $srcPath;
        $loaded = class_exists('App\Lib\Msg\AliyunSms');
    }
}

use App\Lib\Msg\AliyunSms;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 阿里云短信测试工具 - 支持CLI和Web界面
class AliyunSmsTest
{
    private ?AliyunSms $sms = null;
    private Logger $logger;
    private bool $isWebMode;

    public function __construct(bool $useRealCredentials = false)
    {
        $this->isWebMode = PHP_SAPI !== 'cli';

        // 创建日志器
        $this->logger = new Logger('sms_test');
        if ($this->isWebMode) {
            // Web模式：日志输出到内存
            $this->logger->pushHandler(new StreamHandler('php://memory', Logger::DEBUG));
        } else {
            // CLI模式：日志输出到控制台
            $this->logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));
        }

        // 创建SMS实例
        if ($useRealCredentials) {
            try {
                $this->sms = new AliyunSms($this->logger);
            } catch (\Exception $e) {
                // 如果真实凭据不可用，使用测试凭据
                $this->sms = null;
            }
        }

        // 如果没有真实凭据，使用测试凭据进行验证测试
        if (!$this->sms) {
            try {
                $this->sms = new AliyunSms($this->logger, 'test_ak', 'test_sk');
            } catch (\Exception $e) {
                $this->sms = null;
            }
        }
    }

    public function testPhoneValidation(): void
    {
        echo "=== 测试手机号验证 ===\n";

        $testCases = [
            // 有效的手机号
            '8613812345678' => true,   // 中国大陆
            '85261234567' => true,     // 香港
            '85366123456' => true,     // 澳门
            '88691234567' => true,     // 台湾
            '12025551234' => true,     // 美国

            // 无效的手机号
            '138123456789' => false,   // 缺少国家码
            '86138123' => false,       // 太短
            '861381234567890123' => false, // 太长
            '86abc1234567' => false,   // 包含字母
            '' => false,               // 空字符串
        ];

        foreach ($testCases as $phone => $expected) {
            if (!$this->sms)
                continue;

            // 确保手机号是字符串类型
            $phoneStr = (string) $phone;
            $result = $this->sms->sendTemplateMessage($phoneStr, 'SMS_123456', ['code' => '123456']);
            $isValid = strpos($result['message'], '手机号格式非法') === false;

            if ($isValid === $expected) {
                echo "✓ $phone: " . ($expected ? '有效' : '无效') . "\n";
            } else {
                echo "✗ $phone: 预期" . ($expected ? '有效' : '无效') . "，实际" . ($isValid ? '有效' : '无效') . "\n";
            }
        }
    }

    public function testTemplateCodeValidation(): void
    {
        echo "\n=== 测试模板编码验证 ===\n";

        $testCases = [
            'SMS_123456' => true,      // 有效格式
            'SMS_987654321' => true,   // 有效格式（长数字）
            'sms_123456' => false,     // 小写
            'SMS_abc123' => false,     // 包含字母
            'SMS123456' => false,      // 缺少下划线
            'TEMPLATE_123' => false,   // 错误前缀
            '' => false,               // 空字符串
        ];

        foreach ($testCases as $template => $expected) {
            if (!$this->sms)
                continue;

            // 确保模板编码是字符串类型
            $templateStr = (string) $template;
            $result = $this->sms->sendTemplateMessage('8613812345678', $templateStr, ['code' => '123456']);
            $isValid = strpos($result['message'], '模板编码格式非法') === false;

            if ($isValid === $expected) {
                echo "✓ $template: " . ($expected ? '有效' : '无效') . "\n";
            } else {
                echo "✗ $template: 预期" . ($expected ? '有效' : '无效') . "，实际" . ($isValid ? '有效' : '无效') . "\n";
            }
        }
    }

    public function testErrorHandling(): void
    {
        echo "\n=== 测试错误处理 ===\n";

        if (!$this->sms) {
            echo "✗ SMS实例未初始化\n";
            return;
        }

        // 测试缺少签名
        $result = $this->sms->sendTemplateMessage('8613812345678', 'SMS_123456', ['code' => '123456']);
        if (strpos($result['message'], '缺少短信签名') !== false) {
            echo "✓ 正确检测到缺少签名错误\n";
        } else {
            echo "✗ 未能检测到缺少签名错误\n";
        }

        // 测试返回数据结构
        if (isset($result['success'], $result['message'], $result['data'])) {
            echo "✓ 返回数据结构正确\n";
        } else {
            echo "✗ 返回数据结构不正确\n";
        }
    }

    /**
     * 发送真实短信（需要有效的阿里云凭据）
     */
    public function sendRealSms(string $phone, string $templateCode, array $params = [], ?string $signName = null): array
    {
        if (!$this->sms) {
            return ['success' => false, 'message' => 'SMS服务未初始化', 'data' => []];
        }

        return $this->sms->sendTemplateMessage($phone, $templateCode, $params, $signName);
    }

    public function runAllTests(): void
    {
        echo "开始运行AliyunSms改进验证测试...\n\n";

        $this->testPhoneValidation();
        $this->testTemplateCodeValidation();
        $this->testErrorHandling();

        echo "\n测试完成！\n";
    }

    /**
     * 渲染Web UI界面
     */
    public function renderWebUI(): string
    {
        $hasCredentials = $this->sms !== null;
        $credentialStatus = $hasCredentials ?
            '<span style="color: green;">✓ 已配置</span>' :
            '<span style="color: red;">✗ 未配置或无效</span>';

        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云短信测试工具</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .status { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #007bff; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; color: #555; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box; }
        input:focus, textarea:focus, select:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0,123,255,0.25); }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .examples { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px; }
        .examples h4 { margin-top: 0; color: #666; }
        .examples ul { margin: 10px 0; padding-left: 20px; }
        .examples li { margin: 5px 0; }
        .tabs { display: flex; margin-bottom: 20px; border-bottom: 1px solid #ddd; }
        .tab { padding: 10px 20px; cursor: pointer; border-bottom: 2px solid transparent; }
        .tab.active { border-bottom-color: #007bff; color: #007bff; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 阿里云短信测试工具</h1>

        <div class="status">
            <strong>配置状态：</strong> ' . $credentialStatus . '<br>
            <small>如需发送真实短信，请确保已正确配置环境变量：ALIYUN_SMS_ACCESS_KEY_ID、ALIYUN_SMS_ACCESS_KEY_SECRET、ALIYUN_SMS_SIGN_NAME</small>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="showTab(\'send\')">发送短信</div>
            <div class="tab" onclick="showTab(\'test\')">验证测试</div>
        </div>

        <div id="send-tab" class="tab-content active">
            <form method="POST" action="">
                <input type="hidden" name="action" value="send">

                <div class="form-group">
                    <label for="phone">手机号码 *</label>
                    <input type="text" id="phone" name="phone" placeholder="请输入手机号（如：8613812345678）" required>
                </div>

                <div class="form-group">
                    <label for="template">模板编码 *</label>
                    <input type="text" id="template" name="template" placeholder="请输入模板编码（如：SMS_123456789）" required>
                </div>

                <div class="form-group">
                    <label for="params">模板参数（JSON格式）</label>
                    <textarea id="params" name="params" rows="3" placeholder=\'{"code": "123456", "product": "测试产品"}\'></textarea>
                </div>

                <div class="form-group">
                    <label for="sign">短信签名（可选）</label>
                    <input type="text" id="sign" name="sign" placeholder="留空则使用环境变量中的默认签名">
                </div>

                <button type="submit" class="btn">发送短信</button>
                <button type="button" class="btn btn-secondary" onclick="fillExample()">填入示例</button>
            </form>

            <div class="examples">
                <h4>📝 使用说明</h4>
                <ul>
                    <li><strong>手机号：</strong>必须包含国家码，如中国大陆：8613812345678</li>
                    <li><strong>模板编码：</strong>在阿里云控制台申请的模板编码，如：SMS_123456789</li>
                    <li><strong>模板参数：</strong>JSON格式，对应模板中的变量，如：{"code": "123456"}</li>
                    <li><strong>短信签名：</strong>在阿里云控制台申请的签名，留空使用默认签名</li>
                </ul>
            </div>
        </div>

        <div id="test-tab" class="tab-content">
            <form method="POST" action="">
                <input type="hidden" name="action" value="test">
                <p>运行验证测试，检查手机号格式验证、模板编码验证等功能是否正常。</p>
                <button type="submit" class="btn">运行验证测试</button>
            </form>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll(\'.tab-content\').forEach(el => el.classList.remove(\'active\'));
            document.querySelectorAll(\'.tab\').forEach(el => el.classList.remove(\'active\'));

            // 显示选中的标签页
            document.getElementById(tabName + \'-tab\').classList.add(\'active\');
            event.target.classList.add(\'active\');
        }

        function fillExample() {
            document.getElementById(\'phone\').value = \'8613812345678\';
            document.getElementById(\'template\').value = \'SMS_123456789\';
            document.getElementById(\'params\').value = \'{"code": "123456"}\';
            document.getElementById(\'sign\').value = \'\';
        }
    </script>
</body>
</html>';
    }
}

// 主执行逻辑
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    // 如果还没有加载autoloader，尝试加载
    if (!class_exists('App\Lib\Msg\AliyunSms')) {
        $autoloadPaths = [
            __DIR__ . '/../boot.php',
            __DIR__ . '/../vendor/autoload.php'
        ];

        foreach ($autoloadPaths as $path) {
            if (file_exists($path)) {
                require_once $path;
                break;
            }
        }
    }

    try {
        if (PHP_SAPI === 'cli') {
            // CLI模式：运行验证测试
            $test = new AliyunSmsTest();
            $test->runAllTests();
        } else {
            // Web模式：处理HTTP请求
            $test = new AliyunSmsTest(true); // 使用真实凭据

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $action = $_POST['action'] ?? '';

                if ($action === 'send') {
                    // 处理发送短信请求
                    $phone = trim($_POST['phone'] ?? '');
                    $template = trim($_POST['template'] ?? '');
                    $paramsJson = trim($_POST['params'] ?? '{}');
                    $sign = trim($_POST['sign'] ?? '') ?: null;

                    $result = null;
                    $error = '';

                    // 验证输入
                    if (empty($phone) || empty($template)) {
                        $error = '手机号和模板编码不能为空';
                    } else {
                        // 解析JSON参数
                        $params = [];
                        if (!empty($paramsJson)) {
                            $decoded = json_decode($paramsJson, true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $params = $decoded;
                            } else {
                                $error = 'JSON参数格式错误: ' . json_last_error_msg();
                            }
                        }

                        if (empty($error)) {
                            $result = $test->sendRealSms($phone, $template, $params, $sign);
                        }
                    }

                    // 显示结果
                    header('Content-Type: text/html; charset=utf-8');
                    echo $test->renderWebUI();

                    if (!empty($error)) {
                        echo '<div class="result error"><strong>错误：</strong>' . htmlspecialchars($error) . '</div>';
                    } elseif ($result) {
                        $resultClass = $result['success'] ? 'success' : 'error';
                        $resultTitle = $result['success'] ? '发送成功' : '发送失败';

                        echo '<div class="result ' . $resultClass . '">';
                        echo '<strong>' . $resultTitle . '：</strong>' . htmlspecialchars($result['message']) . '<br>';

                        if (!empty($result['data'])) {
                            echo '<details style="margin-top: 10px;"><summary>详细信息</summary>';
                            echo '<pre>' . htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
                            echo '</details>';
                        }
                        echo '</div>';
                    }

                } elseif ($action === 'test') {
                    // 处理验证测试请求
                    header('Content-Type: text/html; charset=utf-8');
                    echo $test->renderWebUI();

                    echo '<div class="result info">';
                    echo '<strong>验证测试结果：</strong><br>';
                    echo '<pre>';
                    ob_start();
                    $test->runAllTests();
                    $testOutput = ob_get_clean();
                    echo htmlspecialchars($testOutput);
                    echo '</pre>';
                    echo '</div>';
                }
            } else {
                // GET请求：显示表单
                header('Content-Type: text/html; charset=utf-8');
                echo $test->renderWebUI();
            }
        }
    } catch (\Throwable $e) {
        if (PHP_SAPI === 'cli') {
            echo "错误: " . $e->getMessage() . "\n";
            echo "请确保已正确安装依赖并配置autoloader\n";
        } else {
            header('Content-Type: text/html; charset=utf-8');
            echo '<div style="padding: 20px; background: #f8d7da; color: #721c24; border-radius: 5px; margin: 20px;">';
            echo '<strong>错误：</strong>' . htmlspecialchars($e->getMessage()) . '<br>';
            echo '<small>请确保已正确安装依赖并配置环境变量</small>';
            echo '</div>';
        }
    }
}
