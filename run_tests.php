<?php
declare(strict_types=1);

echo "=== 阿里云短信测试工具启动器 ===\n";

// 1. 检查并修复autoloader
echo "1. 检查autoloader...\n";
$autoloadPath = __DIR__ . '/vendor/autoload.php';
if (!file_exists($autoloadPath)) {
    echo "   ✗ vendor/autoload.php 不存在\n";
    echo "   正在尝试生成autoloader...\n";
    
    $composerCommands = ['composer dump-autoload', 'php composer.phar dump-autoload'];
    $success = false;
    
    foreach ($composerCommands as $cmd) {
        echo "   尝试: $cmd\n";
        $output = [];
        $returnCode = 0;
        exec($cmd . ' 2>&1', $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($autoloadPath)) {
            echo "   ✓ autoloader生成成功\n";
            $success = true;
            break;
        }
    }
    
    if (!$success) {
        echo "   ✗ 无法生成autoloader，请手动运行: composer dump-autoload\n";
        exit(1);
    }
} else {
    echo "   ✓ vendor/autoload.php 存在\n";
}

// 2. 加载依赖
echo "\n2. 加载依赖...\n";
try {
    require_once $autoloadPath;
    echo "   ✓ autoloader加载成功\n";
} catch (\Throwable $e) {
    echo "   ✗ autoloader加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 3. 检查类是否可用
echo "\n3. 检查类可用性...\n";
$requiredClasses = [
    'App\Lib\Msg\AliyunSms',
    'Monolog\Logger',
    'Monolog\Handler\StreamHandler'
];

foreach ($requiredClasses as $class) {
    if (class_exists($class)) {
        echo "   ✓ $class\n";
    } else {
        echo "   ✗ $class 不可用\n";
        exit(1);
    }
}

// 4. 检查环境变量
echo "\n4. 检查环境变量...\n";
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    echo "   ✓ .env 文件存在\n";
    
    // 尝试加载.env
    if (class_exists('Dotenv\Dotenv')) {
        try {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__);
            $dotenv->safeLoad();
            echo "   ✓ .env 文件加载成功\n";
        } catch (\Throwable $e) {
            echo "   ⚠ .env 文件加载失败: " . $e->getMessage() . "\n";
        }
    }
} else {
    echo "   ⚠ .env 文件不存在\n";
}

$envVars = [
    'ALIYUN_SMS_ACCESS_KEY_ID',
    'ALIYUN_SMS_ACCESS_KEY_SECRET',
    'ALIYUN_SMS_SIGN_NAME'
];

$hasCredentials = true;
foreach ($envVars as $var) {
    $value = getenv($var);
    if ($value !== false && $value !== '') {
        echo "   ✓ $var 已配置\n";
    } else {
        echo "   ⚠ $var 未配置\n";
        $hasCredentials = false;
    }
}

// 5. 运行测试
echo "\n5. 运行测试...\n";
if (PHP_SAPI === 'cli') {
    echo "   CLI模式：运行验证测试\n";
    try {
        require_once __DIR__ . '/tests/AliyunSmsTest.php';
        $test = new AliyunSmsTest();
        $test->runAllTests();
    } catch (\Throwable $e) {
        echo "   ✗ 测试失败: " . $e->getMessage() . "\n";
        echo "   文件: " . $e->getFile() . "\n";
        echo "   行号: " . $e->getLine() . "\n";
        exit(1);
    }
} else {
    echo "   Web模式：请访问测试页面\n";
}

// 6. 显示使用说明
echo "\n=== 使用说明 ===\n";
if ($hasCredentials) {
    echo "✓ 环境变量已配置，可以发送真实短信\n";
} else {
    echo "⚠ 环境变量未完全配置，只能进行验证测试\n";
    echo "  要发送真实短信，请配置以下环境变量：\n";
    foreach ($envVars as $var) {
        echo "  - $var\n";
    }
}

echo "\nCLI使用：\n";
echo "  php tests/AliyunSmsTest.php\n";
echo "\nWeb使用：\n";
echo "  php -S localhost:8080\n";
echo "  然后访问: http://localhost:8080/tests/AliyunSmsTest.php\n";

echo "\n=== 测试工具就绪 ===\n";
