<?php
namespace App\Lib;

use Models\Database;
use Monolog\Logger;
use Models\SerialGenerator;
use Models\SoftwareCatalog;
use App\Lib\Templating\TwigFactory;

class PayTools
{
    /**
     * 支付工具类
     *
     * 包含支付相关的工具方法。
     */

    private $pdo;
    private $logger;

    public function __construct()
    {
        $this->pdo = Database::getInstance();
        $this->logger = new Logger('PayTools');  // 先初始化 Logger 实例
        $logPath = rtrim($_ENV['LOG_PATH'], '/\\');
        if (!is_dir($logPath)) {
            mkdir($logPath, 0777, true);
        }
        $this->logger->pushHandler(new \Monolog\Handler\RotatingFileHandler($logPath . 'error.log', 0, Logger::DEBUG));
    }

    /**
     * 发送授权码邮件（基于 Twig 模板 templates/email/activation_code.twig）。
     *
     * @param string      $toEmail  收件人邮箱
     * @param array       $payload  渲染变量：software_name/activation_code/order_number/download_url 等
     * @param Logger|null $logger   可选日志器
     *
     * @throws \InvalidArgumentException
     * @throws \Exception
     */
    public static function sendActivationEmail(string $toEmail, array $payload = [], ?Logger $logger = null): void
    {
        if (!filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('收件人邮箱无效');
        }

        $activationCode = $payload['activation_code'] ?? ($payload['soft_key'] ?? '');
        if ($activationCode === '') {
            throw new \InvalidArgumentException('缺少必需的授权码（activation_code/soft_key）');
        }

        // 组装模板变量（带默认值）
        $vars = [
            'company_name'     => $_ENV['COMPANY_NAME']    ?? '洛阳夏冰软件技术有限公司',
            'software_name'    => $payload['software_name'] ?? ($payload['soft_name'] ?? ''),
            'activation_code'  => $activationCode,
            'download_url'     => $payload['download_url'] ?? ($_ENV['DOWNLOAD_URL'] ?? ''),
            'service_phone'    => $_ENV['SERVICE_PHONE']   ?? '************',
            'service_email'    => $_ENV['SERVICE_EMAIL']   ?? '<EMAIL>',
            'work_time'        => $_ENV['SERVICE_WORKTIME']?? '工作日 9:00 - 12:00  14:00 - 17:30',
            'website_url'      => $_ENV['WEBSITE_URL']     ?? 'https://www.jiamisoft.com/',
            'order_number'     => $payload['order_number'] ?? ($payload['out_trade_no'] ?? ''),
            'send_time'        => date('Y-m-d H:i:s'),
            'qr_code_url'      => $payload['qr_code_url']  ?? ($_ENV['MAIL_KE_PIC'] ?? ''),
        ];

        // 频率限制/去重：默认 5 分钟内对同一签名只发送一次（可通过 ACTIVATION_WINDOW_SECONDS 配置）
        $windowSeconds = (int) ($_ENV['ACTIVATION_WINDOW_SECONDS'] ?? 300);
        $signature = sha1(
            $toEmail . '|' .
            ($vars['activation_code'] ?? '') . '|' .
            ($vars['order_number'] ?? '')
        );
        if (!self::shouldSendActivationEmail($signature, $windowSeconds)) {
            if ($logger instanceof Logger) {
                $logger->info('跳过授权邮件（限频命中）: ' . ($vars['order_number'] ?? ''));
            }
            return;
        }

        // 异步发送（与系统错误报警一致的思路）：
        // 1) 不因客户端断开而终止 2) 尝试在响应结束后继续执行
        ignore_user_abort(true);
        if (function_exists('fastcgi_finish_request')) {
            @fastcgi_finish_request();
        }

        // 渲染模板并发送（带失败重试）
        $twig   = TwigFactory::createEmailEnvironment();
        $html   = $twig->render('activation_code.twig', $vars);
        $subject= '【软件授权】' . ($vars['software_name'] !== '' ? $vars['software_name'] : '授权信息');
        $mailer = new Mailer($logger);
        $maxAttempts = 3;
        $delayMs     = [250, 1000]; // 渐进延时（毫秒）
        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                $mailer->send($toEmail, $subject, $html, [], true);
                return;
            } catch (\Throwable $e) {
                if ($logger instanceof Logger) {
                    $logger->warning(sprintf('授权邮件发送失败（第%d次）: %s', $attempt, $e->getMessage()));
                }
                if ($attempt >= $maxAttempts) {
                    throw $e; // 超过重试次数，抛出让上层捕获
                }
                $sleep = $delayMs[min($attempt - 1, count($delayMs) - 1)] ?? 1000;
                usleep($sleep * 1000);
            }
        }
    }

    /**
     * 授权邮件限频：同一签名在窗口期内只发一次。
     */
    private static function shouldSendActivationEmail(string $signature, int $windowSeconds = 300): bool
    {
        try {
            $basePath = rtrim($_ENV['LOG_PATH'] ?? sys_get_temp_dir(), '/\\');
            $dir  = $basePath . '/alerts';
            if (!is_dir($dir)) {
                @mkdir($dir, 0755, true);
            }
            $file = $dir . '/activation_emails.json';

            $data = [];
            if (is_file($file)) {
                $json = file_get_contents($file);
                $decoded = json_decode($json, true);
                if (is_array($decoded)) {
                    $data = $decoded;
                }
            }

            $now  = time();
            $last = (int)($data[$signature]['last'] ?? 0);
            if ($now - $last < $windowSeconds) {
                $data[$signature]['count'] = (int)($data[$signature]['count'] ?? 0) + 1;
                @file_put_contents($file, json_encode($data, JSON_UNESCAPED_UNICODE));
                return false;
            }

            $data[$signature] = [
                'last'  => $now,
                'count' => 1,
            ];
            @file_put_contents($file, json_encode($data, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Throwable $e) {
            return true; // 发生异常时允许发送，避免误伤
        }
    }

     public static function genOrderSN()
    {
        $yCode = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
        return $yCode[intval(date('Y')) - 2014] . strtoupper(dechex(date('m'))) . date('d') . substr(time(), -5) . substr(microtime(), 2, 5) . sprintf('%02d', rand(0, 99));
    }

    public static function isOKSoftId(string $soft_id): bool
    {
        $softid_arr = array('BFE0001', 'UE00002', 'MTS0003', 'BSE0004', 'PME0005', 'ASF0006', 'BDL0007', 'EFG0008', 'CFR0009', 'ASD0010', 'OTH0011', 'SFE0012', 'VE00013', 'MCE0012', 'FRE0015', 'MTS0013', 'MTS0023', 'VE00023', 'VE00033');

        if (in_array($soft_id, $softid_arr)) {
            return true;
        }

        return false;
    }

    /**
     * 根据软件ID获取软件名称和价格。
     *
     * @param string $soft_id 软件ID
     * @return array|null 包含 soft_name 和 soft_price,validity_period的关联数组，如果未找到则返回 null。
     */
    public static function getSoftInfo(string $soft_id): ?array
    {
        $sql = "SELECT soft_name, soft_price, validity_period FROM soft_info WHERE soft_id = :soft_id";

        try {
            $pdo = Database::getInstance();
            $stmt = $pdo->prepare($sql);
            $stmt->execute([':soft_id' => $soft_id]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result ?: null;
        } catch (\PDOException $e) {
            throw new \Exception('获取软件信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取订单信息
     * @return array|null 订单信息数组,如果处理失败返回null
     */
    public static function getOrderInfo(): ?array
    {
        $orderInfo = array();

        if (!isset($_POST['paymode'])) {
            $orderInfo['bankID'] = 'ALIPLAY';
            if (isset($_POST['bankId'])) {
                 $orderInfo['bankID'] = htmlspecialchars($_POST['bankId']);
            }

            if ('ALIPLAY' ==  $orderInfo['bankID']) {
                 $orderInfo['paymode'] = 1;
            } else {
                //默认支付方式
                 $orderInfo['paymethod'] = 'bankPay';
                //必填
                 $orderInfo['paymode'] = 2;

                 $orderInfo['defaultbank'] = $orderInfo['bankID'];
            }
        } else {
            //支付方式
             $orderInfo['paymode'] = htmlspecialchars($_POST['paymode']);

            if (2 == $orderInfo['paymode']) {
                //默认支付方式
                 $orderInfo['paymethod'] = 'bankPay';
                //必填
                $orderInfo['bankID']      = htmlspecialchars($_POST['bankId']);
                $orderInfo['defaultbank'] = $orderInfo['bankID'];
            } else if (3 == $orderInfo['paymode']) {
                $orderInfo['paymode'] = 3;
            } else if (1 == $orderInfo['paymode']) {
                $orderInfo['paymode'] = 1;
            } else if (4 == $orderInfo['paymode']) {
                $orderInfo['paymode'] = 4;
            }
        }


         $orderInfo['link'] = '';
        if (isset($_POST['link'])) {
            $orderInfo['link'] = htmlspecialchars($_POST['link']);
            if (60 < strlen($orderInfo['link'])) {
                $orderInfo['link'] = substr($orderInfo['link'], 60);
            }
        }

        //用户邮箱
        $orderInfo['useremail'] = '<EMAIL>'; // 默认邮箱
        if (!empty($_POST['useremail'])) {
            $email = trim($_POST['useremail']);
            if (filter_var($email, FILTER_VALIDATE_EMAIL) && strlen($email) <= 128) {
                $orderInfo['useremail'] = htmlspecialchars($email);
            }
        }

        //用户手机号
        $orderInfo['usermtel'] = ''; // 默认空
        if (!empty($_POST['usermtel'])) {
            $mtel = trim($_POST['usermtel']);
            // 简单的手机号验证，例如中国大陆的11位手机号
            if (preg_match('/^1[3-9]\d{9}$/', $mtel)) {
                $orderInfo['usermtel'] = htmlspecialchars($mtel);
            }
        }

        //购买数量
         $orderInfo['orsoqty'] = 1;
        if (isset($_POST['orsoqty'])) {
            $orderInfo['orsoqty'] = htmlspecialchars($_POST['orsoqty']);

            if (strstr($orderInfo['orsoqty'], '<')) {
                $orderInfo['orsoqty'] = 1;
            }

            if (1 > $orderInfo['orsoqty'] or 20000 < $orderInfo['orsoqty']) {
                $orderInfo['orsoqty'] = 1;
            }
        }

        //软件价格
        $orderInfo['soft_price'] = 99;
        if (isset($_POST['soft_price'])) {
            $orderInfo['soft_price'] = htmlspecialchars($_POST['soft_price']);
        }

        //软件版本号
        $orderInfo['soft_ver'] = '';
        if (isset($_POST['softver'])) {
            $orderInfo['soft_ver'] = htmlspecialchars($_POST['softver']);
        }

        $orderInfo['out_trade_no'] = self::genOrderSN();

        //下单时间
        $orderInfo['order_time'] = date("Y-m-d H:i:s");

        return $orderInfo;
    }

    /**
     * 获取发票信息
     * @return array 发票信息数组
     */
    public static function getInvoiceInfo(): array
    {
        $invoiceInfo = [
            'orinvoice'       => 0,
            'orinvoicehead'   => '',
            'username'        => '',
            'usertel'         => '',
            'address'         => '',
            'post'            => '',
            'shuihao'         => '',
            'province'        => '',
            'city'            => '',
            'county'          => '',
            'e_contact'       => '',
            'e_address'       => '',
            'e_bank'          => '',
            'e_bankaccout'    => '',
            'e_phone'         => '',
        ];

        $province = !empty($_POST['province']) ? htmlspecialchars(trim($_POST['province'])) : '';
        $city = !empty($_POST['city']) ? htmlspecialchars(trim($_POST['city'])) : '';
        $county = !empty($_POST['county']) ? htmlspecialchars(trim($_POST['county'])) : '';

        $invoiceInfo['province'] = $province;
        $invoiceInfo['city'] = $city;
        $invoiceInfo['county'] = $county;

        $invoiceInfo['orinvoicehead'] = !empty($_POST['orinvoicehead']) ? htmlspecialchars(trim($_POST['orinvoicehead'])) : '';
        $invoiceInfo['username'] = !empty($_POST['username']) ? htmlspecialchars(trim($_POST['username'])) : '';
        $invoiceInfo['usertel'] = !empty($_POST['usertel']) ? substr(htmlspecialchars(trim($_POST['usertel'])), 0, 32) : '';
        $invoiceInfo['address'] = !empty($_POST['address']) ? substr($province . $city . $county . htmlspecialchars(trim($_POST['address'])), 0, 256) : '';
        $invoiceInfo['post'] = !empty($_POST['post']) ? htmlspecialchars(trim($_POST['post'])) : '';
        $invoiceInfo['shuihao'] = !empty($_POST['shuihao']) ? htmlspecialchars(trim($_POST['shuihao'])) : '';
        $invoiceInfo['e_contact'] = !empty($_POST['e-contact']) ? htmlspecialchars(trim($_POST['e-contact'])) : '';
        $invoiceInfo['e_address'] = !empty($_POST['e-address']) ? htmlspecialchars(trim($_POST['e-address'])) : '';
        $invoiceInfo['e_bank'] = !empty($_POST['e-bank']) ? htmlspecialchars(trim($_POST['e-bank'])) : '';
        $invoiceInfo['e_bankaccout'] = !empty($_POST['e-bankaccount']) ? htmlspecialchars(trim($_POST['e-bankaccount'])) : '';
        $invoiceInfo['e_phone'] = !empty($_POST['e-phone']) ? htmlspecialchars(trim($_POST['e-phone'])) : '';

        if (!empty($_POST['orinvoice'])) {
            $orinvoiceType = $_POST['orinvoice'];
            if ('invoice-1' === $orinvoiceType || 'invoice-2' === $orinvoiceType) {
                $invoiceInfo['orinvoice'] = 1; // 纸质发票
                if (in_array($province, ['新疆', '西藏', '内蒙古'])) {
                    $invoiceInfo['orinvoice'] = 2; // 纸质发票（偏远地区）
                }
            } elseif ('invoice-3' === $orinvoiceType) {
                $invoiceInfo['orinvoice'] = 4; // 电子发票
            }
        }

        return $invoiceInfo;
    }

    public static function createOrder(array $orderInfo, array $invoiceInfo, array $clientInfo): bool
    {
        $sql = "INSERT INTO soft_order(
                    order_id, soft_id, order_time, payment_method, purchase_quantity, 
                    soft_price, order_price, email, phone, ip, order_tag, soft_ver, os, 
                    orinvoice, orinvoicehead, username, shuihao, usertel, address, post, 
                    user_agent, ueremail, e_address, e_phone, e_bank, e_bankaccount
                ) VALUES (
                    :order_id, :soft_id, :order_time, :payment_method, :purchase_quantity, 
                    :soft_price, :order_price, :email, :phone, :ip, :order_tag, :soft_ver, :os, 
                    :orinvoice, :orinvoicehead, :username, :shuihao, :usertel, :address, :post, 
                    :user_agent, :ueremail, :e_address, :e_phone, :e_bank, :e_bankaccount
                )";

        try {
            $pdo = Database::getInstance();
            $stmt = $pdo->prepare($sql);

            return $stmt->execute([
                ':order_id'          => $orderInfo['out_trade_no'],
                ':soft_id'           => $orderInfo['soft_id'], // 注意：此字段需要从外部传入
                ':order_time'        => $orderInfo['order_time'],
                ':payment_method'    => $orderInfo['paymode'],
                ':purchase_quantity' => $orderInfo['orsoqty'],
                ':soft_price'        => $orderInfo['soft_price'],
                ':order_price'       => $orderInfo['total_fee'], // 注意：total_fee需要计算后传入
                ':email'             => $orderInfo['useremail'],
                ':phone'             => $orderInfo['usermtel'],
                ':ip'                => $clientInfo['ip'],
                ':order_tag'         => $orderInfo['link'],
                ':soft_ver'          => $orderInfo['soft_ver'],
                ':os'                => $clientInfo['os'],
                ':orinvoice'         => $invoiceInfo['orinvoice'],
                ':orinvoicehead'     => $invoiceInfo['orinvoicehead'],
                ':username'          => $invoiceInfo['username'],
                ':shuihao'           => $invoiceInfo['shuihao'],
                ':usertel'           => $invoiceInfo['usertel'],
                ':address'           => $invoiceInfo['address'],
                ':post'              => $invoiceInfo['post'],
                ':user_agent'        => $clientInfo['browser'],
                ':ueremail'          => $invoiceInfo['e_contact'], // 字段映射 ueremail -> e_contact
                ':e_address'         => $invoiceInfo['e_address'],
                ':e_phone'           => $invoiceInfo['e_phone'],
                ':e_bank'            => $invoiceInfo['e_bank'],
                ':e_bankaccount'     => $invoiceInfo['e_bankaccout'],
            ]);
        } catch (\PDOException $e) {
            throw new \Exception('Order creation failed: ' . $e->getMessage());
            return false;
        }
    }

    public static function getTotalFee($orsoqty, $soft_price, $orinvoice = 0)
    {
        $total_fee = $soft_price;

        if (1 == $orsoqty) {
            $total_fee = $soft_price;
        } else if (2 <= $orsoqty && 5 > $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.85);
        } else if (5 <= $orsoqty && 10 > $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.75);
        } else if (10 <= $orsoqty && 25 > $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.7);
        } else if (25 <= $orsoqty && 50 > $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.65);
        } else if (50 <= $orsoqty && 100 > $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.6);
        } else if (100 <= $orsoqty) {
            $total_fee = $orsoqty * (int) ($soft_price * 0.5);
        }

        if (1 == $orinvoice || 3 == $orinvoice) {
            $total_fee += 10;
        } else if (2 == $orinvoice) {
            $total_fee += 25;
        }

        return $total_fee;
    }
    
    public static function getDBOrderInfo($order_id)
    {
        $sql = "SELECT * FROM soft_order WHERE order_id = :order_id";
        try {
            $pdo = Database::getInstance();
            $stmt = $pdo->prepare($sql);
            $stmt->execute([':order_id' => $order_id]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result ?: null;
        } catch (\PDOException $e) {
            throw new \Exception('获取订单信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新订单信息
     * 
     * @param string $order_id 订单号
     * @param array $updateData 需要更新的数据，键为字段名，值为字段值
     * @return bool 更新是否成功
     * @throws \Exception 更新失败时抛出异常
     */
    public static function updateDBOrderInfo($order_id, array $updateData)
    {
        if (empty($updateData)) {
            return true; // 没有需要更新的数据，直接返回成功
        }
        
        try {
            $pdo = Database::getInstance();
            
            // 构建SET子句和参数数组
            $setClause = [];
            $params = [':order_id' => $order_id];
            
            foreach ($updateData as $field => $value) {
                $paramName = ':' . $field;
                $setClause[] = "`$field` = $paramName";
                $params[$paramName] = $value;
            }
            
            // 构建完整的SQL语句
            $sql = "UPDATE soft_order SET " . implode(', ', $setClause) . " WHERE order_id = :order_id";
            
            // 准备并执行SQL语句
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);
            
            if (!$result) {
                throw new \Exception('更新订单信息失败: ' . implode(', ', $stmt->errorInfo()));
            }
            
            return true;
        } catch (\PDOException $e) {
            // 记录错误日志
            throw new \Exception('更新订单信息失败: ' . $e->getMessage());
        }
    }

    function getSoftDownURL($soft_id)
    {
        $softurl = '';

        if ('ASD0010' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/mipansetup.exe';
        } else if ('ASF0006' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/hfsetup.exe';
        } else if ('BDL0007' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/dlsetup.exe';
        } else if ('BFE0001' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/fsesetup.exe';
        } else if ('BSE0004' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/se3000setup.exe';
        } else if ('CFR0009' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/chksetup.exe';
        } else if ('EFG0008' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/fpsetup.exe';
        } else if ('FRE0015' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/foresetup.exe';
        } else if ('MCE0012' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/mcesetup.exe';
        } else if ('MTS0003' == $soft_id || 'MTS0013' == $soft_id || 'MTS0023' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/shutdownsetup.exe';
        } else if ('PME0005' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/pcscsetup.exe';
        } else if ('SFE0012' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/sfe.zip';
        } else if ('UE00002' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/ude.zip';
        } else if ('VE00013' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/vesetup.exe';
        } else if ('VE00023' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/vesetup.exe';
        } else if ('VE00033' == $soft_id) {
            $softurl = 'down.jiamisoft.com/down_new_soft/vesetup.exe';
        }

        return $softurl;
    }
    /**
     * 处理订单 - 更新订单状态并生成授权码
     * 
     * @param string $out_trade_no 外部订单号
     * @param array $filteredParams 已验证的支付参数
     * @param Logger $logger 日志记录器
     * @return array 处理结果
     * @throws \Exception 当订单处理失败时
     */
    public static function processOrder(array $filteredParams, Logger $logger): array
    {
        try {
            // 参数验证
            self::validateProcessOrderParams($filteredParams);
            
            // 获取支付参数
            //商户订单号
            $result['out_trade_no'] = htmlspecialchars($filteredParams['out_trade_no']);
            //交易号
            $result['trade_no']    = htmlspecialchars($filteredParams['trade_no']);
            //订单金额
            $result['total_fee'] = htmlspecialchars($filteredParams['total_amount']);
            //通知时间
            $result['notify_time'] = htmlspecialchars($filteredParams['timestamp']);

            $logger->info("验证成功，外部订单号：" . $result['out_trade_no'] . "，支付宝交易号：" . $result['trade_no'] . "，通知参数：" . json_encode($filteredParams));

            // 获取数据库订单信息
            $dbOrderInfo = self::getDBOrderInfo($result['out_trade_no']);
            if (null == $dbOrderInfo) {
                throw new \Exception('订单不存在');
            }

            // 准备更新数据
            $updateDB = [];
            $result['soft_id']           = $dbOrderInfo['soft_id'];
            $result['soft_key']          = $dbOrderInfo['soft_key'];
            $result['purchase_quantity'] = $dbOrderInfo['purchase_quantity'];
            $result['email']             = $dbOrderInfo['email'];
            $result['phone']             = $dbOrderInfo['phone'];
            $result['payment_time']      = $dbOrderInfo['payment_time'];
            $result['process_time']      = $dbOrderInfo['Process_time'];
            $result['price']             = $dbOrderInfo['order_price'];
            $result['link']              = $dbOrderInfo['order_tag'];

            // 设置处理时间
            if (empty($result['process_time'])) {
                $result['process_time'] = date('Y-m-d H:i:s');
                $updateDB['Process_time'] = $result['process_time'];
            }

            // 设置支付时间
            if (empty($result['payment_time'])) {
                $result['payment_time'] = $result['notify_time'];
                $updateDB['payment_time'] = $result['payment_time'];
            }

            // 获取软件信息
            $soft_info = self::getSoftInfo($dbOrderInfo['soft_id']);
            if (null == $soft_info) {
                throw new \Exception('软件信息不存在');
            }

            $validity_period = $soft_info['validity_period'];

            // 如果处理时间为空，开始处理订单
            if (empty($dbOrderInfo['Process_time'])) {
                $result['soft_key'] = $dbOrderInfo['soft_key'];
                
                // 如果授权码为空生成授权码
                if (empty($result['soft_key'])) {
                    // 验证授权码生成的前置条件
                    self::validateLicenseGenerationConditions($dbOrderInfo, $soft_info);
                    
                    $validity_period = $soft_info['validity_period'];
                    // 计算授权到期时间：订单处理时间 + 有效期
                    $result['expire_time'] = date('Y-m-d H:i:s', strtotime($result['process_time'] . " +{$validity_period} days"));
                    $SID = 0;
                    
                    if (!\Models\SoftwareCatalog::codeToId($dbOrderInfo['soft_id'], $SID)) {
                        throw new \Exception('获取ID失败');
                    }
                    
                    $contact = $dbOrderInfo['phone'];
                    if (empty($contact)) {
                        $contact = $dbOrderInfo['email'];
                    }
                    
                    $result['soft_key'] = \Models\SerialGenerator::generateWithExpiryString(1, $SID, $contact, $result['expire_time']);

                    if (empty($result['soft_key'])) {
                        throw new \Exception('生成授权码失败');
                    } else {
                        $updateDB['soft_key'] = $result['soft_key'];
                        $logger->info("成功生成授权码，订单号: {$result['out_trade_no']}, 授权码: {$result['soft_key']}, 有效期至: {$result['expire_time']}");
                    }
                }
            } else{
                // 如果订单已经处理过，根据处理时间计算过期时间
                $result['expire_time'] = date('Y-m-d H:i:s', strtotime($dbOrderInfo['Process_time'] . " +{$validity_period} days"));
            }

            // 更新数据库
            if ($updateDB) {
                self::updateDBOrderInfo($result['out_trade_no'], $updateDB);
                $logger->info("数据库更新成功，订单号: {$result['out_trade_no']}, 更新字段: " . json_encode(array_keys($updateDB)));
            } else {
                $logger->info("订单无需更新，订单号: {$result['out_trade_no']}");
            }

            $logger->info("订单处理完成，订单号: {$result['out_trade_no']}, 交易号: {$result['trade_no']}, 授权码: {$result['soft_key']}");

            // 异步发送授权码邮件（使用限频/重试策略）
            try {
                if (!empty($result['email']) && filter_var($result['email'], FILTER_VALIDATE_EMAIL)) {
                    $download_url = self::getSoftDownURL($result['soft_id']);
                    self::sendActivationEmail($result['email'], [
                        'software_name'   => $soft_info['soft_name'] ?? '',
                        'activation_code' => $result['soft_key'],
                        'order_number'    => $result['out_trade_no'],
                        'download_url'    => $download_url,
                    ], $logger);
                } else {
                    $logger->warning('未发送授权邮件：收件邮箱无效或为空');
                }
            } catch (\Throwable $mailEx) {
                $logger->error('发送授权邮件失败: ' . $mailEx->getMessage());
            }

            $result['page_title'] = '订单号：' . $result['out_trade_no'] . '支付成功完成。';
            return $result;

        } catch (\Exception $e) {
            $logger->error("订单处理失败: " . $e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 验证processOrder方法的参数
     * 
     * @param string $out_trade_no 外部订单号
     * @param array $filteredParams 支付参数
     * @throws \InvalidArgumentException 当参数无效时
     */
    private static function validateProcessOrderParams(array $filteredParams): void
    {        
        // 验证必需的支付参数
        $requiredParams = ['out_trade_no', 'trade_no', 'total_amount', 'timestamp'];
        foreach ($requiredParams as $param) {
            if (!isset($filteredParams[$param]) || empty($filteredParams[$param])) {
                throw new \InvalidArgumentException("缺少必需参数: {$param}");
            }
        }
        
        // 验证支付宝交易号格式
        if (!preg_match('/^[0-9]{28}$/', $filteredParams['trade_no'])) {
            throw new \InvalidArgumentException('支付宝交易号格式无效');
        }
        
        // 验证金额格式
        if (!is_numeric($filteredParams['total_amount']) || floatval($filteredParams['total_amount']) <= 0) {
            throw new \InvalidArgumentException('支付金额必须为正数');
        }
        
        // 验证时间戳格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $filteredParams['timestamp'])) {
            throw new \InvalidArgumentException('时间戳格式无效，应为 YYYY-MM-DD HH:MM:SS 格式');
        }
        
        // 验证时间戳是否为有效日期
        $timestamp = strtotime($filteredParams['timestamp']);
        if ($timestamp === false) {
            throw new \InvalidArgumentException('时间戳无效');
        }
        
        // 验证时间戳不能是未来时间（允许5分钟误差）
        if ($timestamp > time() + 300) {
            throw new \InvalidArgumentException('时间戳不能是未来时间');
        }
        
        // 验证时间戳不能太久远（不能超过30天前）
        if ($timestamp < time() - (30 * 24 * 3600)) {
            throw new \InvalidArgumentException('时间戳过期（超过30天）');
        }
    }

    /**
     * 验证并清理订单号
     * 
     * @param string $out_trade_no 原始订单号
     * @return string 清理后的订单号
     * @throws \InvalidArgumentException 当订单号无效时
     */
    public static function sanitizeOrderNo(string $out_trade_no): string
    {
        // 移除可能的恶意字符
        $sanitized = preg_replace('/[^A-Za-z0-9\-_]/', '', $out_trade_no);
        
        if (empty($sanitized)) {
            throw new \InvalidArgumentException('订单号包含无效字符');
        }
        
        return $sanitized;
    }

    /**
     * 验证软件授权码生成的前置条件
     * 
     * @param array $dbOrderInfo 数据库订单信息
     * @param array $soft_info 软件信息
     * @throws \Exception 当前置条件不满足时
     */
    private static function validateLicenseGenerationConditions(array $dbOrderInfo, array $soft_info): void
    {
        // 验证有效期
        if (!isset($soft_info['validity_period']) || !is_numeric($soft_info['validity_period'])) {
            throw new \Exception('软件有效期配置无效');
        }
        
        if (intval($soft_info['validity_period']) <= 0) {
            throw new \Exception('软件有效期必须大于0天');
        }
        
        // 验证联系方式
        $contact = $dbOrderInfo['phone'] ?? '';
        if (empty($contact)) {
            $contact = $dbOrderInfo['email'] ?? '';
        }
        
        if (empty($contact)) {
            throw new \Exception('生成授权码需要提供手机号或邮箱');
        }
        
        // 验证邮箱格式
        if (!empty($dbOrderInfo['email']) && !filter_var($dbOrderInfo['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('邮箱格式无效');
        }
        
        // 验证手机号格式（中国大陆）
        if (!empty($dbOrderInfo['phone']) && !preg_match('/^1[3-9]\d{9}$/', $dbOrderInfo['phone'])) {
            throw new \Exception('手机号格式无效');
        }
    }
}