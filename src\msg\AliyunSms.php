<?php
declare(strict_types=1);

namespace App\Lib\Msg;

use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageWithTemplateRequest;
use Darabonba\OpenApi\Models\Config as OpenApiConfig;
use Monolog\Logger;

final class AliyunSms
{
    private Dysmsapi $client;
    private ?Logger $logger;

    public function __construct(?Logger $logger = null, ?string $accessKeyId = null, ?string $accessKeySecret = null, ?string $endpoint = null)
    {
        $this->logger = $logger;

        $ak = $accessKeyId ?? ($_ENV['ALIYUN_SMS_ACCESS_KEY_ID'] ?? '');
        $sk = $accessKeySecret ?? ($_ENV['ALIYUN_SMS_ACCESS_KEY_SECRET'] ?? '');
        $ep = $endpoint ?? ($_ENV['ALIYUN_SMS_ENDPOINT'] ?? 'dysmsapi.aliyuncs.com');

        if ($ak === '' || $sk === '') {
            throw new \InvalidArgumentException('缺少阿里云短信密钥，请配置 ALIYUN_SMS_ACCESS_KEY_ID 与 ALIYUN_SMS_ACCESS_KEY_SECRET');
        }

        $config = new OpenApiConfig([
            'accessKeyId' => $ak,
            'accessKeySecret' => $sk,
            'endpoint' => $ep,
        ]);

        $this->client = new Dysmsapi($config);
    }

    /**
     * 发送国内模板短信（SendMessageWithTemplate）。
     *
     * @param string      $phoneE164    目标手机号（必须包含国家码的纯数字，如中国大陆 86138xxxxxxx，不带+）
     * @param string      $templateCode 模板编码（如 SMS_123456）
     * @param array       $params       模板变量，例如 ['code' => '123456']
     * @param string|null $signName     短信签名（默认取 ALIYUN_SMS_SIGN_NAME）
     *
     * @return array{success:bool,message:string,data:array}
     */
    public function sendTemplateMessage(string $phoneE164, string $templateCode, array $params = [], ?string $signName = null): array
    {
        $from = $signName ?? ($_ENV['ALIYUN_SMS_SIGN_NAME'] ?? '');
        if ($from === '') {
            return ['success' => false, 'message' => '缺少短信签名（ALIYUN_SMS_SIGN_NAME）', 'data' => []];
        }

        if (!preg_match('/^\d{8,20}$/', $phoneE164)) {
            return ['success' => false, 'message' => '手机号格式非法，需包含国家码的纯数字', 'data' => []];
        }

        $request = new SendMessageWithTemplateRequest([
            'to' => $phoneE164,
            'from' => $from,
            'templateCode' => $templateCode,
            'templateParam' => empty($params) ? null : json_encode($params, JSON_UNESCAPED_UNICODE),
        ]);

        try {
            $response = $this->client->sendMessageWithTemplate($request);
            $body = $response->body ?? null;

            $result = [
                'requestId' => $body->requestId ?? '',
                'responseCode' => $body->responseCode ?? '',
                'responseDescription' => $body->responseDescription ?? '',
                'messageId' => $body->messageId ?? '',
                'to' => $body->to ?? '',
                'numberDetail' => $body->numberDetail ?? null,
                'segments' => $body->segments ?? null,
            ];

            $ok = strtoupper((string)($body->responseCode ?? '')) === 'OK';
            $msg = $result['responseDescription'] ?: ($ok ? 'OK' : 'FAILED');
            return ['success' => $ok, 'message' => $msg, 'data' => $result];
        } catch (\Throwable $e) {
            if ($this->logger instanceof Logger) {
                $this->logger->error('Aliyun SMS 发送失败: ' . $e->getMessage(), ['exception' => get_class($e)]);
            }
            return ['success' => false, 'message' => $e->getMessage(), 'data' => []];
        }
    }
}

