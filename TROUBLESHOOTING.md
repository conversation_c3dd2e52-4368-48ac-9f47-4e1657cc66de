# 阿里云短信测试工具故障排除指南

## 问题1：Class 'App\Lib\Msg\AliyunSms' not found

### 🔧 **已修复** - 类型错误问题
**问题：** `Argument 1 passed to AliyunSms::sendTemplateMessage() must be of the type string, int given`
**解决：** 已在测试代码中添加类型转换，确保所有参数都是正确的类型。

## 问题2：Class 'App\Lib\Msg\AliyunSms' not found

### 🔍 问题原因
这个错误通常是由以下原因引起的：
1. Composer autoloader未正确生成
2. composer.json中的autoload配置有误
3. 类文件路径不正确

### 🛠️ 解决方案

#### 方案1：重新生成Autoloader（推荐）
```bash
# 在项目根目录执行
composer dump-autoload

# 或者如果有composer.phar
php composer.phar dump-autoload
```

#### 方案2：使用修复脚本
```bash
# 运行修复脚本
php fix_autoload.php
```

#### 方案3：手动验证和修复
1. **检查composer.json**
   ```json
   {
       "autoload": {
           "psr-4": {
               "App\\Lib\\": "src/",
               "Models\\": "models/"
           }
       }
   }
   ```

2. **检查文件是否存在**
   ```bash
   # 检查AliyunSms.php是否存在
   ls -la src/msg/AliyunSms.php
   
   # 检查vendor/autoload.php是否存在
   ls -la vendor/autoload.php
   ```

3. **重新安装依赖**
   ```bash
   composer install --no-dev
   ```

#### 方案4：直接运行测试（临时解决）
我已经修改了测试文件，现在它会自动尝试多种加载方式：

```bash
# 直接运行测试文件
php tests/AliyunSmsTest.php
```

### 🌐 Web界面访问

#### 使用PHP内置服务器
```bash
# 在项目根目录启动服务器
php -S localhost:8080

# 然后访问
http://localhost:8080/tests/AliyunSmsTest.php
```

#### 使用现有Web服务器
如果您已经有Web服务器运行，直接访问：
```
http://your-domain/tests/AliyunSmsTest.php
```

### 📋 验证步骤

#### 1. 验证Autoloader
```bash
php -r "
require_once 'vendor/autoload.php';
if (class_exists('App\Lib\Msg\AliyunSms')) {
    echo 'AliyunSms类加载成功\n';
} else {
    echo 'AliyunSms类加载失败\n';
}
"
```

#### 2. 验证环境变量
```bash
# 检查环境变量是否设置
echo $ALIYUN_SMS_ACCESS_KEY_ID
echo $ALIYUN_SMS_ACCESS_KEY_SECRET
echo $ALIYUN_SMS_SIGN_NAME
```

#### 3. 运行诊断脚本
```bash
php tests/test_autoload.php
```

### 🔧 常见问题解决

#### 问题1：composer命令不存在
```bash
# 下载composer
curl -sS https://getcomposer.org/installer | php

# 使用composer.phar
php composer.phar dump-autoload
```

#### 问题2：权限问题
```bash
# 给予执行权限
chmod +x composer.phar

# 或者使用sudo（Linux/Mac）
sudo composer dump-autoload
```

#### 问题3：网络问题
```bash
# 使用国内镜像
composer config repo.packagist composer https://mirrors.aliyun.com/composer/
composer dump-autoload
```

### 📱 测试短信发送

#### 配置环境变量
```bash
# Windows
set ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
set ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
set ALIYUN_SMS_SIGN_NAME=your_sign_name

# Linux/Mac
export ALIYUN_SMS_ACCESS_KEY_ID=your_access_key_id
export ALIYUN_SMS_ACCESS_KEY_SECRET=your_access_key_secret
export ALIYUN_SMS_SIGN_NAME=your_sign_name
```

#### 测试发送
1. 访问Web界面：`http://localhost:8080/tests/AliyunSmsTest.php`
2. 填入测试数据：
   - 手机号：`8613812345678`
   - 模板编码：`SMS_123456789`
   - 模板参数：`{"code": "123456"}`
3. 点击"发送短信"

### 🆘 如果仍然无法解决

1. **检查PHP版本**
   ```bash
   php -v
   # 确保PHP版本 >= 7.4
   ```

2. **检查扩展**
   ```bash
   php -m | grep -E "(json|curl|openssl)"
   ```

3. **查看详细错误**
   ```bash
   php -d display_errors=1 tests/AliyunSmsTest.php
   ```

4. **联系技术支持**
   - 提供完整的错误信息
   - 提供PHP版本和操作系统信息
   - 提供composer.json内容

---

*如果按照以上步骤仍无法解决问题，请提供详细的错误信息以便进一步诊断。*
