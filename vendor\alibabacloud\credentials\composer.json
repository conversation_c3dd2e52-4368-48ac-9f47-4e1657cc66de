{"name": "alibabacloud/credentials", "homepage": "https://www.alibabacloud.com/", "description": "Alibaba Cloud Credentials for PHP", "keywords": ["sdk", "tool", "cloud", "client", "<PERSON><PERSON><PERSON>", "library", "alibaba", "Credentials", "alibabacloud"], "type": "library", "license": "Apache-2.0", "support": {"source": "https://github.com/aliyun/credentials-php", "issues": "https://github.com/aliyun/credentials-php/issues"}, "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "require": {"php": ">=5.6", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0"}, "require-dev": {"ext-spl": "*", "ext-dom": "*", "ext-pcre": "*", "psr/cache": "^1.0", "ext-sockets": "*", "drupal/coder": "^8.3", "symfony/dotenv": "^3.4", "phpunit/phpunit": "^5.7|^6.6|^9.3", "monolog/monolog": "^1.24", "composer/composer": "^1.8", "mikey179/vfsstream": "^1.6", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "autoload-dev": {"psr-4": {"AlibabaCloud\\Credentials\\Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "optimize-autoloader": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "minimum-stability": "dev", "prefer-stable": true, "scripts-descriptions": {"cs": "Tokenizes PHP, JavaScript and CSS files to detect violations of a defined coding standard.", "cbf": "Automatically correct coding standard violations.", "fixer": "Fixes code to follow standards.", "test": "Run all tests.", "unit": "Run Unit tests.", "feature": "Run Feature tests.", "clearCache": "Clear cache like coverage.", "coverage": "Show Coverage html.", "endpoints": "Update endpoints from OSS."}, "scripts": {"cs": "phpcs --standard=PSR2 -n ./", "cbf": "phpcbf --standard=PSR2 -n ./", "fixer": "php-cs-fixer fix ./", "test": ["phpunit --colors=always"], "unit": ["@clearCache", "phpunit --testsuite=Unit --colors=always"], "feature": ["@clearCache", "phpunit --testsuite=Feature --colors=always"], "coverage": "open cache/coverage/index.html", "clearCache": "rm -rf cache/*"}}