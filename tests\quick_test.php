<?php
declare(strict_types=1);

// 快速测试脚本 - 验证类型转换修复
echo "=== 快速类型测试 ===\n";

// 尝试多种方式加载依赖
$loaded = false;

// 方式1: 尝试加载boot.php
$bootPath = __DIR__ . '/../boot.php';
if (file_exists($bootPath)) {
    try {
        require_once $bootPath;
        $loaded = class_exists('App\Lib\Msg\AliyunSms');
    } catch (\Throwable $e) {
        echo "Boot.php加载失败: " . $e->getMessage() . "\n";
    }
}

// 方式2: 如果boot.php失败，直接加载vendor/autoload.php
if (!$loaded) {
    $autoloadPath = __DIR__ . '/../vendor/autoload.php';
    if (file_exists($autoloadPath)) {
        require_once $autoloadPath;
        $loaded = class_exists('App\Lib\Msg\AliyunSms');
    }
}

if (!$loaded) {
    echo "✗ 无法加载AliyunSms类\n";
    exit(1);
}

echo "✓ AliyunSms类加载成功\n";

// 测试类型转换
use App\Lib\Msg\AliyunSms;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

try {
    // 创建测试实例
    $logger = new Logger('test');
    $logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));
    $sms = new AliyunSms($logger, 'test_ak', 'test_sk');
    
    echo "✓ AliyunSms实例创建成功\n";
    
    // 测试不同类型的手机号
    $testPhones = [
        '8613812345678',    // 字符串
        8613812345678,      // 整数（这个会导致错误）
        '85261234567',      // 字符串
    ];
    
    echo "\n=== 测试类型转换 ===\n";
    
    foreach ($testPhones as $phone) {
        echo "测试手机号: " . var_export($phone, true) . " (类型: " . gettype($phone) . ")\n";
        
        try {
            // 确保转换为字符串
            $phoneStr = (string)$phone;
            $result = $sms->sendTemplateMessage($phoneStr, 'SMS_123456', ['code' => '123456']);
            echo "  ✓ 调用成功，返回: " . $result['message'] . "\n";
        } catch (\Throwable $e) {
            echo "  ✗ 调用失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "现在可以安全运行: php tests/AliyunSmsTest.php\n";
    
} catch (\Throwable $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
