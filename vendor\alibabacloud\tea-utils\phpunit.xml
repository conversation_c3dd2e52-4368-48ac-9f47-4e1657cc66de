<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" bootstrap="tests/bootstrap.php" colors="true" executionOrder="depends,defects" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.0/phpunit.xsd" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <coverage>
    <include>
      <directory suffix=".php">src</directory>
    </include>
    <report>
      <clover outputFile="cache/coverage.clover"/>
      <html outputDirectory="cache/coverage" lowUpperBound="35" highLowerBound="70"/>
    </report>
  </coverage>
  <logging/>
  <testsuites>
    <testsuite name="default">
      <directory suffix="Test.php">tests</directory>
    </testsuite>
  </testsuites>
</phpunit>
