# 支付与授权服务（pay.jiamisoft.com)

### 概览
- 技术栈：PHP 7.4+/8.x、Composer、Monolog、<PERSON><PERSON><PERSON><PERSON><PERSON>、Alipay SDK、WechatPay v3 SDK
- 主要功能：支付宝/微信支付回调处理、订单处理与授权码生成、邮件通知
- 入口引导：`boot.php`（加载 .env、初始化日志与错误处理）

## 快速开始
### 环境要求
- PHP 7.4+（推荐 8.x），开启 OpenSSL、curl、mbstring、json、pdo_mysql 等扩展
- Nginx/Apache 已配置根目录到本项目
- Composer 依赖已安装

### 运行配置（.env）
至少包含：
```
APP_ENV=production
APP_DEBUG=false
APP_URL=https://pay.jiamisoft.com
TIMEZONE=Asia/Shanghai
LOG_PATH=/www/wwwroot/pay.jiamisoft.com/logs

# 邮件发送（必填项）
MAIL_HOST=smtp.qq.com
MAIL_PORT=465
MAIL_ENCRYPTION=ssl
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_smtp_token
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=系统通知
ADM_EMAIL=<EMAIL>

# 微信支付（示例）
WECHAT_APP_ID=wx123...
WECHAT_MCH_ID=1900...
WECHAT_SERIAL_NO=...
WECHAT_PRIVATE_KEY_PATH=/path/to/apiclient_key.pem
WECHAT_PLATFORM_SERIAL_NO=...
WECHAT_PLATFORM_CERT_PATH=/path/to/wechat_platform.pem
WECHAT_API_V3_KEY=32bytes_key_string

# 授权生成
SERVER_KEY=base64_server_key
CLIENT_KEY=base64_client_key
KEY_T0=1700000000
```

### 数据库配置
`models/Database.php` 读取 `/www/wwwroot/config/db/soft-shop.php` 内部常量：
```
PC_DB_HOST=...
PC_DB_NAME=...
PC_DB_USER=...
PC_DB_PASSWORD=...
PC_DB_CHARSET=utf8mb4
```
请在该文件中定义以上常量（非 .env）。

## 目录结构
- `boot.php`：全局引导（.env、日志、错误/异常处理）
- `aliNotify.php` / `aliReturn.php`：支付宝 PC 异步/同步回调
- `aliWapNotify.php` / `aliWapReturn.php`：支付宝 WAP 异步/同步回调
- `wxNotify.php`、`WxOrderquery.php`：微信支付回调/查询
- `src/`
  - `Alipay/`：支付宝下单封装与配置
  - `wx/WxPay.php`：微信 v3 下单/回调/查询
  - `Mailer.php`：邮件发送封装（PHPMailer），含环境变量校验
  - `MailException.php`、`Common.php`、`Base32.php`、`PayTools.php`
- `models/`：`Database.php`、`SerialGenerator.php`、`SoftwareCatalog.php`
- `templates/email/`：`activation_code.twig`、`system_error_alert.twig`
- `view/`：回执渲染页（ali/wx）

## 邮件模板与变量
- `templates/email/activation_code.twig`
  - 常用变量：`company_name, software_name, activation_code, download_url, service_phone, service_email, work_time, website_url, order_number, send_time, qr_code_url`
- `templates/email/system_error_alert.twig`
  - 常用变量：`error_title, error_message, error_file, error_line, error_time, error_level, error_color, company_name, server_info, php_version, memory_usage, memory_peak, user_info.ip, user_info.os, user_info.browser, user_info.request_uri, base_url, send_time, error_id`

### 发送邮件示例（代码）
```php
use App\Lib\Mailer;

$mailer = new Mailer(); // 从 .env 读取 MAIL_*
$html = '<h3>测试</h3><p>内容...</p>';
$mailer->send($_ENV['ADM_EMAIL'], '系统测试邮件', $html, [], true);
```
说明：
- `src/Mailer.php` 会校验必需变量：`MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD`，并验证发件人邮箱合法性（`MAIL_FROM_ADDRESS` 或回退 `MAIL_USERNAME`）。

## 支付回调与业务
- 支付宝：
  - 异步：`/aliNotify.php`（验签 → 白名单过滤 → `PayTools::processOrder` → 返回 `success`/`fail`）
  - 同步：`/aliReturn.php`（验签 → 业务处理 → 渲染 `view/ali/orderquery.php`）
- 支付宝 WAP：
  - 异步：`/aliWapNotify.php`；同步：`/aliWapReturn.php`
- 微信：
  - 回调：`/wxNotify.php`（验签、解密 resource），下单/查询见 `src/wx/WxPay.php`

注意：
- 回调应根据实际订单金额、商户号、app_id 做二次校验并保证幂等。
- 日志位置：
  - `boot.php`：`$_ENV['LOG_PATH']/error.log`
  - 支付宝/微信模块：`$_ENV['LOG_PATH']` 子目录或 `./logs/...`（视文件实现）

## 授权码生成
- `Models\SerialGenerator::generate(...)`
  - 依赖环境变量：`SERVER_KEY, CLIENT_KEY, KEY_T0`
  - 返回 6-6-6-6-6-6 格式序列号

## 安全与运维
- 生产环境：`APP_DEBUG=false`
- 不要将密钥、证书、数据库账号提交到仓库
- 放通 SMTP 出口端口（465/587），避免发送阻塞导致 502
- Nginx 超时配置应大于邮件或第三方 API 超时

## 开发状态跟踪
- 已完成：支付宝/微信回调接入、授权码生成、邮件封装与校验、日志体系、视图页面
- 待完善：订单金额校验、回调幂等、统一错误码表（建议 `doc/errCode.md`）、单元测试用例

## 常见问题
- 502 或长时间无响应：多为 SMTP 连接/SSL 握手阻塞，请检查 `MAIL_*`、网络出口连通性、证书验证。
- 邮件报发件人非法：确保 `MAIL_FROM_ADDRESS`（或 `MAIL_USERNAME`）为合法邮箱。

## 短信发送（阿里云）

已集成 SDK：`alibabacloud/dysmsapi-20180501`（2018-05-01 版本）。封装类：`App\Lib\Msg\AliyunSms`。

### 环境变量
- `ALIYUN_SMS_ACCESS_KEY_ID`：访问密钥 ID
- `ALIYUN_SMS_ACCESS_KEY_SECRET`：访问密钥密钥
- `ALIYUN_SMS_SIGN_NAME`：短信签名（控制台“签名管理”的名称）
- `ALIYUN_SMS_ENDPOINT`：可选，默认 `dysmsapi.aliyuncs.com`

手机号需为不带加号的 E.164 数字格式（示例：中国大陆 `86138xxxxxxx`）。

本实现使用 `SendMessageWithTemplate`（仅支持中国大陆）。

### 发送示例
```php
$sms = new \App\Lib\Msg\AliyunSms();
$result = $sms->sendTemplateMessage(
    '86' . $mobile,           // 带国家码手机号（无+）
    'SMS_123456789',          // 模板编码
    ['code' => $code],        // 模板变量
    null                      // 可选覆盖签名，不传则用 ALIYUN_SMS_SIGN_NAME
);

if (!$result['success']) {
    // 失败处理与日志
}
```

### 返回结构
```php
[
  'success' => bool,
  'message' => string,        // SDK返回描述或OK/FAILED
  'data' => [
    'requestId' => string,
    'responseCode' => string, // OK 表示成功
    'responseDescription' => string,
    'messageId' => string,
    'to' => string,           // 目标号码（E.164）
    'numberDetail' => mixed,  // 运营商/地区等
    'segments' => string|null // 计费条数
  ]
]
```