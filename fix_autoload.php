<?php
declare(strict_types=1);

echo "=== 修复Autoloader ===\n";

// 1. 检查composer是否存在
$composerPhar = 'composer.phar';
$composerCmd = 'composer';

if (file_exists($composerPhar)) {
    $cmd = "php $composerPhar dump-autoload";
} else {
    $cmd = "$composerCmd dump-autoload";
}

echo "执行命令: $cmd\n";

// 2. 执行composer dump-autoload
$output = [];
$returnCode = 0;
exec($cmd . ' 2>&1', $output, $returnCode);

echo "返回码: $returnCode\n";
echo "输出:\n";
foreach ($output as $line) {
    echo "  $line\n";
}

// 3. 验证autoloader是否正常
echo "\n=== 验证修复结果 ===\n";

if (file_exists('vendor/autoload.php')) {
    echo "✓ vendor/autoload.php 存在\n";
    
    try {
        require_once 'vendor/autoload.php';
        echo "✓ autoload.php 加载成功\n";
        
        if (class_exists('App\Lib\Msg\AliyunSms')) {
            echo "✓ App\Lib\Msg\AliyunSms 类可用\n";
        } else {
            echo "✗ App\Lib\Msg\AliyunSms 类不可用\n";
        }
        
    } catch (\Throwable $e) {
        echo "✗ autoload.php 加载失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ vendor/autoload.php 不存在\n";
}

echo "\n=== 修复完成 ===\n";
echo "现在可以尝试运行: php tests/AliyunSmsTest.php\n";
echo "或访问Web界面: http://localhost:8080/tests/AliyunSmsTest.php\n";
