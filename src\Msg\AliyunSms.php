<?php
declare(strict_types=1);

namespace App\Lib\Msg;

use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageWithTemplateRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use Darabonba\OpenApi\Models\Config as OpenApiConfig;
use Monolog\Logger;

final class AliyunSms
{
    private Dysmsapi $client;
    private ?Logger $logger;

    public function __construct(?Logger $logger = null, ?string $accessKeyId = null, ?string $accessKeySecret = null, ?string $endpoint = null)
    {
        $this->logger = $logger;

        $ak = $accessKeyId ?? (getenv('ALIYUN_SMS_ACCESS_KEY_ID') ?: '');
        $sk = $accessKeySecret ?? (getenv('ALIYUN_SMS_ACCESS_KEY_SECRET') ?: '');
        $ep = $endpoint ?? (getenv('ALIYUN_SMS_ENDPOINT') ?: 'dysmsapi.aliyuncs.com');

        if ($ak === '' || $sk === '') {
            throw new \InvalidArgumentException('缺少阿里云短信密钥，请配置 ALIYUN_SMS_ACCESS_KEY_ID 与 ALIYUN_SMS_ACCESS_KEY_SECRET');
        }

        $config = new OpenApiConfig([
            'accessKeyId' => $ak,
            'accessKeySecret' => $sk,
            'endpoint' => $ep,
        ]);

        $this->client = new Dysmsapi($config);
    }

    /**
     * 发送国内模板短信（SendMessageWithTemplate）。
     *
     * @param string      $phoneE164    目标手机号（必须包含国家码的纯数字，如中国大陆 86138xxxxxxx，不带+）
     * @param string      $templateCode 模板编码（如 SMS_123456）
     * @param array       $params       模板变量，例如 ['code' => '123456']
     * @param string|null $signName     短信签名（默认取 ALIYUN_SMS_SIGN_NAME）
     *
     * @return array{success:bool,message:string,data:array}
     */
    public function sendTemplateMessage(string $phoneE164, string $templateCode, array $params = [], ?string $signName = null): array
    {
        $from = $signName ?? (getenv('ALIYUN_SMS_SIGN_NAME') ?: '');
        if ($from === '') {
            return ['success' => false, 'message' => '缺少短信签名（ALIYUN_SMS_SIGN_NAME）', 'data' => []];
        }

        // 国内模板短信仅支持中国大陆：收紧为 86 开头的 11 位手机号
        if (!preg_match('/^86(1[3-9]\d{9})$/', $phoneE164)) {
            return ['success' => false, 'message' => '手机号格式非法，仅支持中国大陆号码：例如 86138xxxxxxxx', 'data' => []];
        }

        // 验证模板编码格式
        if (!preg_match('/^SMS_\d+$/', $templateCode)) {
            return ['success' => false, 'message' => '模板编码格式非法，应为SMS_开头的数字编码', 'data' => []];
        }

        $json = '';
        if (!empty($params)) {
            $json = json_encode($params, JSON_UNESCAPED_UNICODE);
            if ($json === false) {
                return ['success' => false, 'message' => '模板参数 JSON 编码失败: ' . json_last_error_msg(), 'data' => ['error_type' => 'json_encode_failed']];
            }
        }

        $request = new SendMessageWithTemplateRequest([
            'to'            => $phoneE164,
            'from'          => $from,
            'templateCode'  => $templateCode,
            'templateParam' => $json ?: null,
        ]);

        // 记录请求日志
        if ($this->logger instanceof Logger) {
            $this->logger->info('发送阿里云短信', [
                'phone' => substr($phoneE164, 0, 3) . '****' . substr($phoneE164, -4), // 脱敏处理
                'template' => $templateCode,
                'sign' => $from
            ]);
        }

        try {
            $response = $this->client->sendMessageWithTemplate($request);
            $body = $response->body ?? null;

            $result = [
                'requestId' => $body->requestId ?? '',
                'responseCode' => $body->responseCode ?? '',
                'responseDescription' => $body->responseDescription ?? '',
                'messageId' => $body->messageId ?? '',
                'to' => $body->to ?? '',
                'numberDetail' => $body->numberDetail ?? null,
                'segments' => $body->segments ?? null,
            ];

            $ok = strtoupper((string) ($body->responseCode ?? '')) === 'OK';
            $msg = $result['responseDescription'] ?: ($ok ? 'OK' : 'FAILED');

            // 记录结果日志
            if ($this->logger instanceof Logger) {
                $logLevel = $ok ? 'info' : 'warning';
                $this->logger->$logLevel('阿里云短信发送结果', [
                    'success' => $ok,
                    'responseCode' => $result['responseCode'],
                    'messageId' => $result['messageId']
                ]);
            }

            return ['success' => $ok, 'message' => $msg, 'data' => $result];
        } catch (\AlibabaCloud\Tea\Exception\TeaError $e) {
            // 阿里云SDK特定错误
            $errorMsg = '阿里云SDK错误: ' . $e->getMessage();
            if ($this->logger instanceof Logger) {
                $this->logger->error($errorMsg, [
                    'exception' => get_class($e),
                    'code' => $e->getCode(),
                    'phone' => substr($phoneE164, 0, 3) . '****' . substr($phoneE164, -4)
                ]);
            }
            return ['success' => false, 'message' => $errorMsg, 'data' => ['error_type' => 'sdk_error']];
        } catch (\InvalidArgumentException $e) {
            // 参数错误
            $errorMsg = '参数错误: ' . $e->getMessage();
            if ($this->logger instanceof Logger) {
                $this->logger->error($errorMsg, ['exception' => get_class($e)]);
            }
            return ['success' => false, 'message' => $errorMsg, 'data' => ['error_type' => 'invalid_argument']];
        } catch (\Throwable $e) {
            // 其他未知错误
            $errorMsg = '未知错误: ' . $e->getMessage();
            if ($this->logger instanceof Logger) {
                $this->logger->error($errorMsg, [
                    'exception' => get_class($e),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
            return ['success' => false, 'message' => $errorMsg, 'data' => ['error_type' => 'unknown_error']];
        }
    }
}

